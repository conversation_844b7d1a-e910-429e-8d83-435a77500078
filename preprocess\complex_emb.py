import os
import torch
import numpy as np
import dgl
import argparse
from dgl.nn.pytorch import RelGraphConv
from torch import nn
import torch.nn.functional as F
from tqdm import tqdm
import pickle

class ComplExModel(nn.Module):
    def __init__(self, num_nodes, num_rels, dim):
        super(ComplExModel, self).__init__()
        self.num_nodes = num_nodes
        self.num_rels = num_rels
        self.dim = dim
        
        # 实部和虚部的实体嵌入
        self.emb_e_real = nn.Embedding(num_nodes, dim)
        self.emb_e_img = nn.Embedding(num_nodes, dim)
        
        # 实部和虚部的关系嵌入
        self.emb_rel_real = nn.Embedding(num_rels, dim)
        self.emb_rel_img = nn.Embedding(num_rels, dim)
        
        # 初始化嵌入
        nn.init.xavier_uniform_(self.emb_e_real.weight)
        nn.init.xavier_uniform_(self.emb_e_img.weight)
        nn.init.xavier_uniform_(self.emb_rel_real.weight)
        nn.init.xavier_uniform_(self.emb_rel_img.weight)
    
    def forward(self, head_ids, rel_ids, tail_ids):
        # 获取头实体的实部和虚部嵌入
        head_real = self.emb_e_real(head_ids)
        head_img = self.emb_e_img(head_ids)
        
        # 获取关系的实部和虚部嵌入
        rel_real = self.emb_rel_real(rel_ids)
        rel_img = self.emb_rel_img(rel_ids)
        
        # 获取尾实体的实部和虚部嵌入
        tail_real = self.emb_e_real(tail_ids)
        tail_img = self.emb_e_img(tail_ids)
        
        # ComplEx模型的评分函数
        # 计算 Re(<h,r,t>) = Re(h)·Re(r)·Re(t) + Re(h)·Im(r)·Im(t) + Im(h)·Re(r)·Im(t) - Im(h)·Im(r)·Re(t)
        score_real = (head_real * rel_real * tail_real + 
                      head_real * rel_img * tail_img + 
                      head_img * rel_real * tail_img - 
                      head_img * rel_img * tail_real)
        
        # 对每个三元组的分数求和
        score = torch.sum(score_real, dim=1)
        return score
    
    def get_embeddings(self):
        # 将实部和虚部嵌入合并为复数嵌入
        real = self.emb_e_real.weight.data
        img = self.emb_e_img.weight.data
        # 将实部和虚部拼接起来作为节点的特征
        return torch.cat([real, img], dim=1)

def train_complex_model(graph, args):
    """
    使用ComplEx模型训练节点嵌入
    
    参数:
    graph: DGL图对象
    args: 参数对象
    
    返回:
    node_embeddings: 训练好的节点嵌入
    """
    device = torch.device(f'cuda:{args.gpu}' if args.gpu >= 0 and torch.cuda.is_available() else 'cpu')
    
    # 获取图的节点数和边类型数
    num_nodes = graph.number_of_nodes()
    num_rels = len(graph.canonical_etypes)
    
    # 创建ComplEx模型
    model = ComplExModel(num_nodes, num_rels, args.dim).to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=args.lr)
    
    # 准备训练数据
    edges = []
    rel_ids = []
    for i, etype in enumerate(graph.canonical_etypes):
        src, dst = graph.edges(etype=etype)
        edges.append((src, dst))
        rel_ids.append(torch.full((len(src),), i, dtype=torch.long))
    
    # 合并所有边和关系ID
    all_src = torch.cat([e[0] for e in edges])
    all_dst = torch.cat([e[1] for e in edges])
    all_rel = torch.cat(rel_ids)
    
    # 创建负采样
    def get_negative_samples(pos_samples, num_nodes, num_neg):
        neg_samples = []
        for _ in range(num_neg):
            # 随机替换尾实体
            neg = pos_samples.clone()
            neg[:, 2] = torch.randint(0, num_nodes, (len(pos_samples),))
            neg_samples.append(neg)
        return torch.cat(neg_samples, dim=0)
    
    # 训练循环
    for epoch in range(args.epochs):
        model.train()
        
        # 构建正样本
        pos_samples = torch.stack([all_src, all_rel, all_dst], dim=1)
        
        # 构建负样本
        neg_samples = get_negative_samples(pos_samples, num_nodes, args.num_neg)
        
        # 计算正样本的分数
        pos_scores = model(pos_samples[:, 0], pos_samples[:, 1], pos_samples[:, 2])
        
        # 计算负样本的分数
        neg_scores = model(neg_samples[:, 0], neg_samples[:, 1], neg_samples[:, 2])
        
        # 使用对比损失函数
        loss = F.margin_ranking_loss(
            pos_scores,
            neg_scores,
            torch.ones_like(pos_scores),
            margin=args.margin
        )
        
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        if (epoch + 1) % 10 == 0:
            print(f"Epoch {epoch+1}/{args.epochs}, Loss: {loss.item():.4f}")
    
    # 获取训练好的节点嵌入
    model.eval()
    with torch.no_grad():
        node_embeddings = model.get_embeddings().cpu().numpy()
    
    return node_embeddings

def process_dataset(dataset_name, args):
    """
    处理指定的数据集
    
    参数:
    dataset_name: 数据集名称
    args: 参数对象
    """
    print(f"Processing dataset: {dataset_name}")
    
    # 加载数据集
    if dataset_name == 'ogbn-mag':
        # 使用DGL加载ogbn-mag数据集
        from ogb.nodeproppred import DglNodePropPredDataset
        dataset = DglNodePropPredDataset(name='ogbn-mag')
        graph, labels = dataset[0]
        
        # 检查哪些节点类型缺少特征
        for ntype in graph.ntypes:
            if 'feat' not in graph.nodes[ntype].data:
                print(f"Node type {ntype} lacks features, generating with ComplEx")
                
                # 训练ComplEx模型
                node_embeddings = train_complex_model(graph, args)
                
                # 保存生成的特征
                save_path = os.path.join(args.output_dir, f"{dataset_name}_{ntype}_complex_emb.npy")
                np.save(save_path, node_embeddings)
                print(f"Saved embeddings for {ntype} to {save_path}")
    
    elif dataset_name in ['DBLP', 'Yelp', 'Pubmed', 'Freebase']:
        # 加载自定义数据集
        data_path = os.path.join(args.data_dir, f"{dataset_name}.pkl")
        
        if dataset_name == 'DBLP':
            with open(data_path, mode="rb") as f:
                edge_index_dict, feature_dict, train_idx, test_idx, labels, num_nodes_dict = pickle.load(f)
            
            # 检查哪些节点类型缺少特征
            for ntype in num_nodes_dict.keys():
                if ntype not in feature_dict:
                    print(f"Node type {ntype} lacks features, generating with ComplEx")
                    
                    # 构建DGL图
                    g = dgl.heterograph(edge_index_dict)
                    
                    # 训练ComplEx模型
                    node_embeddings = train_complex_model(g, args)
                    
                    # 保存生成的特征
                    save_path = os.path.join(args.output_dir, f"{dataset_name}_{ntype}_complex_emb.npy")
                    np.save(save_path, node_embeddings)
                    print(f"Saved embeddings for {ntype} to {save_path}")
        
        elif dataset_name == 'Yelp':
            with open(data_path, mode="rb") as f:
                edge_index_dict, features, pos_link_s, pos_link_t, test_link_s, test_link_t, flags, num_nodes_dict = pickle.load(f)
            
            # 检查哪些节点类型缺少特征
            for ntype in num_nodes_dict.keys():
                if ntype not in features:
                    print(f"Node type {ntype} lacks features, generating with ComplEx")
                    
                    # 构建DGL图
                    g = dgl.heterograph(edge_index_dict)
                    
                    # 训练ComplEx模型
                    node_embeddings = train_complex_model(g, args)
                    
                    # 保存生成的特征
                    save_path = os.path.join(args.output_dir, f"{dataset_name}_{ntype}_complex_emb.npy")
                    np.save(save_path, node_embeddings)
                    print(f"Saved embeddings for {ntype} to {save_path}")
        
        elif dataset_name == 'Pubmed':
            with open(data_path, mode="rb") as f:
                edge_index_dict, feature_dict, train_idx, test_idx, labels, num_nodes_dict = pickle.load(f)
            
            # 检查哪些节点类型缺少特征
            for ntype in num_nodes_dict.keys():
                if ntype not in feature_dict:
                    print(f"Node type {ntype} lacks features, generating with ComplEx")
                    
                    # 构建DGL图
                    g = dgl.heterograph(edge_index_dict)
                    
                    # 训练ComplEx模型
                    node_embeddings = train_complex_model(g, args)
                    
                    # 保存生成的特征
                    save_path = os.path.join(args.output_dir, f"{dataset_name}_{ntype}_complex_emb.npy")
                    np.save(save_path, node_embeddings)
                    print(f"Saved embeddings for {ntype} to {save_path}")
        
        elif dataset_name == 'Freebase':
            with open(data_path, mode="rb") as f:
                edge_index_dict, train_idx, test_idx, labels, num_nodes_dict = pickle.load(f)
            
            # 构建DGL图
            g = dgl.heterograph(edge_index_dict)
            
            # 为所有节点类型生成特征
            for ntype in num_nodes_dict.keys():
                print(f"Generating features for node type {ntype} with ComplEx")
                
                # 训练ComplEx模型
                node_embeddings = train_complex_model(g, args)
                
                # 保存生成的特征
                save_path = os.path.join(args.output_dir, f"{dataset_name}_{ntype}_complex_emb.npy")
                np.save(save_path, node_embeddings)
                print(f"Saved embeddings for {ntype} to {save_path}")
    
    else:
        print(f"Unknown dataset: {dataset_name}")

def main():
    parser = argparse.ArgumentParser(description='Generate node features using ComplEx model')
    parser.add_argument('--data_dir', type=str, default='../dataset', help='Directory containing dataset files')
    parser.add_argument('--output_dir', type=str, default='../embeddings', help='Directory to save generated embeddings')
    parser.add_argument('--datasets', type=str, nargs='+', default=['DBLP', 'Yelp', 'Pubmed', 'Freebase', 'ogbn-mag'], 
                        help='Datasets to process')
    parser.add_argument('--dim', type=int, default=200, help='Embedding dimension')
    parser.add_argument('--lr', type=float, default=0.001, help='Learning rate')
    parser.add_argument('--epochs', type=int, default=100, help='Number of training epochs')
    parser.add_argument('--margin', type=float, default=1.0, help='Margin for ranking loss')
    parser.add_argument('--num_neg', type=int, default=5, help='Number of negative samples per positive sample')
    parser.add_argument('--gpu', type=int, default=0, help='GPU device ID, -1 for CPU')
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 处理每个数据集
    for dataset_name in args.datasets:
        process_dataset(dataset_name, args)

if __name__ == "__main__":
    main() 