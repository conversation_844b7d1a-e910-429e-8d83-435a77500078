import sys
import copy
import os.path as osp
from typing import Optional


import torch
import torch.utils.data
from torch_sparse import SparseTensor
from torch_geometric.utils import to_undirected
from torch_geometric.data import Data, ClusterData
import pickle
from torch_geometric.utils.hetero import group_hetero_graph
import numpy as np



def get_node_id_dblp(id):
    num_phrase = 217557
    num_author = 1766361
    num_venue = 5076
    num_year = 83
    idx_author = num_phrase
    idx_venue = num_phrase + num_author
    idx_year = num_phrase + num_author + num_venue
    num_nodes = num_phrase + num_author + num_venue + num_year

    id = int(id)
    if id < num_phrase:
        return id
    elif id < num_phrase + num_author:
        return id - idx_author
    elif id < num_phrase + num_author + num_venue:
        return id - idx_venue
    else:
        return id - idx_year


def load_DBLP():
    num_nodes_dict = {"author": 1766361, "phrase": 217557, "venue": 5076, "year": 83}
    num_phrase = 217557
    num_author = 1766361
    num_venue = 5076
    num_year = 83
    idx_author = num_phrase
    idx_venue = num_phrase + num_author
    idx_year = num_phrase + num_author + num_venue
    num_nodes = num_phrase + num_author + num_venue + num_year

    path = "../dataset/DBLP/"
    link_file = 'link.dat'
    label_train_file = 'label.dat'
    label_test_file = 'label.dat.test'

    new_nid = torch.tensor([0 for i in range(num_nodes)])
    feature = torch.zeros((num_nodes, 300))
    n_type = torch.tensor([0 for i in range(num_nodes)])
    with open(path + "node.dat", mode="r", encoding="utf-8") as f:
        for line in f:
            s = line.strip("\n").split("\t")
            embedding = list(map(float, s[-1].split(",")))
            feature[int(s[0])] = torch.Tensor(embedding)
            n_type[int(s[0])] = int(s[2])
    
    feature_dict = {}
    for ntype in range(4):
        mask = n_type == ntype
        new_nid[mask] = torch.tensor([i for i in range(torch.sum(mask).item())])
        for node_key in num_nodes_dict.keys():
            if num_nodes_dict[node_key] == torch.sum(mask).item():
                feature_dict[node_key] = feature[mask]
            else:
                continue
    del feature
    print("read feature done")
    new_nid = new_nid.tolist()
    labels = torch.zeros(num_author, dtype=torch.long)
    train_idx = []
    test_idx = []
    with open(path + label_train_file) as f:
        line_data = f.readline()
        while line_data:
            o_id, _, _, label = line_data.split()
            idx = new_nid[int(o_id)]
            train_idx.append(idx)
            labels[idx] = int(label)
            line_data = f.readline()
    print("read train done")
    with open(path + label_test_file) as f:
        line_data = f.readline()
        while line_data:
            o_id, _, _, label = line_data.split()
            idx = new_nid[int(o_id)]
            test_idx.append(idx)
            labels[idx] = int(label)
            line_data = f.readline()
    print("read test done")
    edge_s = {}
    edge_t = {}
    for i in range(6):
        edge_s[i] = []
        edge_t[i] = []
    with open(path + link_file, encoding='utf-8') as f:
        line_data = f.readline()
        while line_data:
            s_id, t_id, link_type, _ = line_data.split()
            s_id = new_nid[int(s_id)]
            t_id = new_nid[int(t_id)]
            edge_s[int(link_type)].append(s_id)
            edge_t[int(link_type)].append(t_id)
            line_data = f.readline()
    print("read link done")
    edge_index_dict = {
        ('phrase', 'cooccur', 'phrase'): torch.stack([torch.tensor(edge_s[0]), torch.tensor(edge_t[0])]),
        ('author', 'coauthor', 'author'): torch.stack([torch.tensor(edge_s[1]), torch.tensor(edge_t[1])]),
        ('author', 'cite', 'author'): torch.stack([torch.tensor(edge_s[2]), torch.tensor(edge_t[2])]),
        ('author', 'study', 'phrase'): torch.stack([torch.tensor(edge_s[3]), torch.tensor(edge_t[3])]),
        ('author', 'publishin', 'venue'): torch.stack([torch.tensor(edge_s[4]), torch.tensor(edge_t[4])]),
        ('author', 'activein', 'year'): torch.stack([torch.tensor(edge_s[5]), torch.tensor(edge_t[5])]),
        ('phrase', 'study_r', 'author'): torch.stack([torch.tensor(edge_t[3]), torch.tensor(edge_s[3])]),
        ('venue', 'publishin_r', 'author'): torch.stack([torch.tensor(edge_t[4]), torch.tensor(edge_s[4])]),
        ('year', 'activein_r', 'author'): torch.stack([torch.tensor(edge_t[5]), torch.tensor(edge_s[5])])
        }
    edge_index = to_undirected(edge_index_dict[('phrase', 'cooccur', 'phrase')])
    edge_index_dict[('phrase', 'cooccur', 'phrase')] = edge_index
    edge_index = to_undirected(edge_index_dict[('author', 'coauthor', 'author')])
    edge_index_dict[('author', 'coauthor', 'author')] = edge_index
    edge_index = to_undirected(edge_index_dict[('author', 'cite', 'author')])
    edge_index_dict[('author', 'cite', 'author')] = edge_index
    
    with open(path + "DBLP.pkl", mode="wb") as f:
        pickle.dump((edge_index_dict, feature_dict, train_idx, test_idx, labels, num_nodes_dict), f)

    exit()

def get_node_id_pubmed(id):
    num_gene = 13561
    num_disease = 20163
    num_chemical = 26522
    num_species = 2863
    idx_disease = num_gene
    idx_chemical = num_gene + num_disease
    idx_species = num_gene + num_disease + num_chemical
    num_nodes = num_gene + num_disease + num_chemical + num_species

    id = int(id)
    if id < num_gene:
        return id
    elif id < num_gene + num_disease:
        return (id - idx_disease)
    elif id < num_gene + num_disease + num_chemical:
        return (id - idx_chemical)
    else:
        return (id - idx_species)

def load_PubMed():
    num_nodes_dict = {"gene": 13561, "disease": 20163, "chemical": 26522, "species": 2863}
    num_gene = 13561
    num_disease = 20163
    num_chemical = 26522
    num_species = 2863
    idx_disease = num_gene
    idx_chemical = num_gene + num_disease
    idx_species = num_gene + num_disease + num_chemical
    num_nodes = num_gene + num_disease + num_chemical + num_species

    path = '../dataset/PubMed/'
    feature_file = 'node.dat'
    link_file = 'link.dat'
    label_train_file = 'label.dat'
    label_test_file = 'label.dat.test'

    new_nid = torch.tensor([0 for i in range(num_nodes)])
    feature = torch.zeros((num_nodes, 200))
    n_type = torch.tensor([0 for i in range(num_nodes)])
    with open(path + "node.dat", mode="r", encoding="utf-8") as f:
        for line in f:
            s = line.strip("\n").split("\t")
            embedding = list(map(float, s[-1].split(",")))
            feature[int(s[0])] = torch.Tensor(embedding)
            n_type[int(s[0])] = int(s[2])
    
    feature_dict = {}
    for ntype in range(4):
        mask = n_type == ntype
        new_nid[mask] = torch.tensor([i for i in range(torch.sum(mask).item())])
        for node_key in num_nodes_dict.keys():
            if num_nodes_dict[node_key] == torch.sum(mask).item():
                feature_dict[node_key] = feature[mask]
            else:
                continue
    
    labels = torch.zeros(num_disease, dtype=int) - 1
    train_idx = []
    test_idx = []
    with open(path + label_train_file) as f:
        line_data = f.readline()
        while (line_data):
            o_id, _, _, label = line_data.split()
            idx = new_nid[int(o_id)]
            train_idx.append(idx)
            labels[idx] = int(label)
            line_data = f.readline()

    with open(path + label_test_file) as f:
        line_data = f.readline()
        while (line_data):
            o_id, _, _, label = line_data.split()
            idx = new_nid[int(o_id)]
            test_idx.append(idx)
            labels[idx] = int(label)
            line_data = f.readline()

    edge_s = {}
    edge_t = {}
    for i in range(10):
        edge_s[i] = []
        edge_t[i] = []
    with open(path + link_file) as f:
        line_data = f.readline()
        while(line_data):
            s_id, t_id, link_type, _ = line_data.split()
            s_id = new_nid[int(s_id)]
            t_id = new_nid[int(t_id)]
            edge_s[int(link_type)].append(s_id)
            edge_t[int(link_type)].append(t_id)
            line_data = f.readline()

    edge_index_dict = {
        ('gene', 'gag', 'gene'): torch.stack([torch.tensor(edge_s[0]), torch.tensor(edge_t[0])]),
        ('gene', 'gcd', 'disease'): torch.stack([torch.tensor(edge_s[1]), torch.tensor(edge_t[1])]),
        ('disease', 'dad', 'disease'): torch.stack([torch.tensor(edge_s[2]), torch.tensor(edge_t[2])]),
        ('chemical', 'cig', 'gene'): torch.stack([torch.tensor(edge_s[3]), torch.tensor(edge_t[3])]),
        ('chemical', 'cid', 'disease'): torch.stack([torch.tensor(edge_s[4]), torch.tensor(edge_t[4])]),
        ('chemical', 'cac', 'chemical'): torch.stack([torch.tensor(edge_s[5]), torch.tensor(edge_t[5])]),
        ('chemical', 'cis', 'species'): torch.stack([torch.tensor(edge_s[6]), torch.tensor(edge_t[6])]),
        ('species', 'swg', 'gene'): torch.stack([torch.tensor(edge_s[7]), torch.tensor(edge_t[7])]),
        ('species', 'swd', 'disease'): torch.stack([torch.tensor(edge_s[8]), torch.tensor(edge_t[8])]),
        ('species', 'sas', 'species'): torch.stack([torch.tensor(edge_s[9]), torch.tensor(edge_t[9])]),

        ('disease', 'gcd_r', 'gene'): torch.stack([torch.tensor(edge_t[1]), torch.tensor(edge_s[1])]),
        ('gene', 'cig_r', 'chemical'): torch.stack([torch.tensor(edge_t[3]), torch.tensor(edge_s[3])]),
        ('disease', 'cid_r', 'chemical'): torch.stack([torch.tensor(edge_t[4]), torch.tensor(edge_s[4])]),
        ('species', 'cis_r', 'chemical'): torch.stack([torch.tensor(edge_t[6]), torch.tensor(edge_s[6])]),
        ('gene', 'swg_r', 'species'): torch.stack([torch.tensor(edge_t[7]), torch.tensor(edge_s[7])]),
        ('disease', 'swd_r', 'species'): torch.stack([torch.tensor(edge_t[8]), torch.tensor(edge_s[8])])
    }
    edge_index = to_undirected(edge_index_dict[('gene', 'gag', 'gene')])
    edge_index_dict[('gene', 'gag', 'gene')] = edge_index
    edge_index = to_undirected(edge_index_dict[('disease', 'dad', 'disease')])
    edge_index_dict[('disease', 'dad', 'disease')] = edge_index
    edge_index = to_undirected(edge_index_dict[('chemical', 'cac', 'chemical')])
    edge_index_dict[('chemical', 'cac', 'chemical')] = edge_index
    edge_index = to_undirected(edge_index_dict[('species', 'sas', 'species')])
    edge_index_dict[('species', 'sas', 'species')] = edge_index

    with open(path + "PubMed.pkl", mode="wb") as f:
        pickle.dump((edge_index_dict, feature_dict, train_idx, test_idx, labels, num_nodes_dict), f)


def load_PubMed_link():
    num_nodes_dict = {"gene": 13561, "disease": 20163, "chemical": 26522, "species": 2863}
    num_gene = 13561
    num_disease = 20163
    num_chemical = 26522
    num_species = 2863
    idx_disease = num_gene
    idx_chemical = num_gene + num_disease
    idx_species = num_gene + num_disease + num_chemical
    num_nodes = num_gene + num_disease + num_chemical + num_species

    path = '../dataset/PubMed/'
    feature_file = 'node.dat'
    link_file = 'link.dat'
    link_test_file = 'link.dat.test'

    new_nid = torch.tensor([0 for i in range(num_nodes)])
    feature = torch.zeros((num_nodes, 200))
    n_type = torch.tensor([0 for i in range(num_nodes)])
    with open(path + "node.dat", mode="r", encoding="utf-8") as f:
        for line in f:
            s = line.strip("\n").split("\t")
            embedding = list(map(float, s[-1].split(",")))
            feature[int(s[0])] = torch.Tensor(embedding)
            n_type[int(s[0])] = int(s[2])
    
    feature_dict = {}
    for ntype in range(4):
        mask = n_type == ntype
        new_nid[mask] = torch.tensor([i for i in range(torch.sum(mask).item())])
        for node_key in num_nodes_dict.keys():
            if num_nodes_dict[node_key] == torch.sum(mask).item():
                feature_dict[node_key] = feature[mask]
            else:
                continue

    test_link_s = []
    test_link_t = []
    flags = []
    with open(path + link_test_file) as f:
        line_data = f.readline()
        while (line_data):
            s_id, t_id, flag = line_data.split()
            s_id = new_nid[int(s_id)]
            t_id = new_nid[int(t_id)]
            test_link_s.append(s_id)
            test_link_t.append(t_id)
            flags.append(int(flag))
            line_data = f.readline()

    edge_s = {}
    edge_t = {}
    for i in range(10):
        edge_s[i] = []
        edge_t[i] = []
    pos_link_s = []
    pos_link_t = []
    with open(path + link_file) as f:
        line_data = f.readline()
        while(line_data):
            s_id, t_id, link_type, _ = line_data.split()
            s_id = new_nid[int(s_id)]
            t_id = new_nid[int(t_id)]
            if link_type == "2":
                pos_link_s.append(s_id)
                pos_link_t.append(t_id)
            edge_s[int(link_type)].append(s_id)
            edge_t[int(link_type)].append(t_id)
            line_data = f.readline()

    edge_index_dict = {
        ('gene', 'gag', 'gene'): torch.stack([torch.tensor(edge_s[0]), torch.tensor(edge_t[0])]),
        ('gene', 'gcd', 'disease'): torch.stack([torch.tensor(edge_s[1]), torch.tensor(edge_t[1])]),
        ('disease', 'dad', 'disease'): torch.stack([torch.tensor(edge_s[2]), torch.tensor(edge_t[2])]),
        ('chemical', 'cig', 'gene'): torch.stack([torch.tensor(edge_s[3]), torch.tensor(edge_t[3])]),
        ('chemical', 'cid', 'disease'): torch.stack([torch.tensor(edge_s[4]), torch.tensor(edge_t[4])]),
        ('chemical', 'cac', 'chemical'): torch.stack([torch.tensor(edge_s[5]), torch.tensor(edge_t[5])]),
        ('chemical', 'cis', 'species'): torch.stack([torch.tensor(edge_s[6]), torch.tensor(edge_t[6])]),
        ('species', 'swg', 'gene'): torch.stack([torch.tensor(edge_s[7]), torch.tensor(edge_t[7])]),
        ('species', 'swd', 'disease'): torch.stack([torch.tensor(edge_s[8]), torch.tensor(edge_t[8])]),
        ('species', 'sas', 'species'): torch.stack([torch.tensor(edge_s[9]), torch.tensor(edge_t[9])]),

        ('disease', 'gcd_r', 'gene'): torch.stack([torch.tensor(edge_t[1]), torch.tensor(edge_s[1])]),
        ('gene', 'cig_r', 'chemical'): torch.stack([torch.tensor(edge_t[3]), torch.tensor(edge_s[3])]),
        ('disease', 'cid_r', 'chemical'): torch.stack([torch.tensor(edge_t[4]), torch.tensor(edge_s[4])]),
        ('species', 'cis_r', 'chemical'): torch.stack([torch.tensor(edge_t[6]), torch.tensor(edge_s[6])]),
        ('gene', 'swg_r', 'species'): torch.stack([torch.tensor(edge_t[7]), torch.tensor(edge_s[7])]),
        ('disease', 'swd_r', 'species'): torch.stack([torch.tensor(edge_t[8]), torch.tensor(edge_s[8])])
    }
    edge_index = to_undirected(edge_index_dict[('gene', 'gag', 'gene')])
    edge_index_dict[('gene', 'gag', 'gene')] = edge_index
    edge_index = to_undirected(edge_index_dict[('disease', 'dad', 'disease')])
    edge_index_dict[('disease', 'dad', 'disease')] = edge_index
    edge_index = to_undirected(edge_index_dict[('chemical', 'cac', 'chemical')])
    edge_index_dict[('chemical', 'cac', 'chemical')] = edge_index
    edge_index = to_undirected(edge_index_dict[('species', 'sas', 'species')])
    edge_index_dict[('species', 'sas', 'species')] = edge_index

    with open(path + "PubMed_link.pkl", mode="wb") as f:
        pickle.dump((edge_index_dict, feature_dict, pos_link_s, pos_link_t, test_link_s, test_link_t, flags, num_nodes_dict), f)


def load_DBLP_link():
    num_nodes_dict = {"author": 1766361, "phrase": 217557, "venue": 5076, "year": 83}
    num_phrase = 217557
    num_author = 1766361
    num_venue = 5076
    num_year = 83
    idx_author = num_phrase
    idx_venue = num_phrase + num_author
    idx_year = num_phrase + num_author + num_venue
    num_nodes = num_phrase + num_author + num_venue + num_year

    path = "../dataset/DBLP/"
    link_file = 'link.dat'
    link_train_file = 'link.dat'
    link_test_file = 'link.dat.test'

    new_nid = torch.tensor([0 for i in range(num_nodes)])
    feature = torch.zeros((num_nodes, 300))
    n_type = torch.tensor([0 for i in range(num_nodes)])
    with open(path + "node.dat", mode="r", encoding="utf-8") as f:
        for line in f:
            s = line.strip("\n").split("\t")
            embedding = list(map(float, s[-1].split(",")))
            feature[int(s[0])] = torch.Tensor(embedding)
            n_type[int(s[0])] = int(s[2])
    
    feature_dict = {}
    for ntype in range(4):
        mask = n_type == ntype
        new_nid[mask] = torch.tensor([i for i in range(torch.sum(mask).item())])
        for node_key in num_nodes_dict.keys():
            if num_nodes_dict[node_key] == torch.sum(mask).item():
                feature_dict[node_key] = feature[mask]
            else:
                continue
    new_nid = new_nid.tolist()
    test_link_s = []
    test_link_t = []
    flags = []
    with open(path + link_test_file) as f:
        line_data = f.readline()
        while (line_data):
            s_id, t_id, flag = line_data.split()
            s_id = new_nid[int(s_id)]
            t_id = new_nid[int(t_id)]
            test_link_s.append(s_id)
            test_link_t.append(t_id)
            flags.append(int(flag))
            line_data = f.readline()

    edge_s = {}
    edge_t = {}
    for i in range(6):
        edge_s[i] = []
        edge_t[i] = []
    pos_link_s = []
    pos_link_t = []
    with open(path + link_file, encoding='utf-8') as f:
        line_data = f.readline()
        while line_data:
            s_id, t_id, link_type, _ = line_data.split()
            s_id = new_nid[int(s_id)]
            t_id = new_nid[int(t_id)]
            if int(link_type) == 1:
                pos_link_s.append(s_id)
                pos_link_t.append(t_id)
            edge_s[int(link_type)].append(s_id)
            edge_t[int(link_type)].append(t_id)
            line_data = f.readline()
    
    edge_index_dict = {
        ('phrase', 'cooccur', 'phrase'): torch.stack([torch.tensor(edge_s[0]), torch.tensor(edge_t[0])]),
        ('author', 'coauthor', 'author'): torch.stack([torch.tensor(edge_s[1]), torch.tensor(edge_t[1])]),
        ('author', 'cite', 'author'): torch.stack([torch.tensor(edge_s[2]), torch.tensor(edge_t[2])]),
        ('author', 'study', 'phrase'): torch.stack([torch.tensor(edge_s[3]), torch.tensor(edge_t[3])]),
        ('author', 'publishin', 'venue'): torch.stack([torch.tensor(edge_s[4]), torch.tensor(edge_t[4])]),
        ('author', 'activein', 'year'): torch.stack([torch.tensor(edge_s[5]), torch.tensor(edge_t[5])]),
        ('phrase', 'study_r', 'author'): torch.stack([torch.tensor(edge_t[3]), torch.tensor(edge_s[3])]),
        ('venue', 'publishin_r', 'author'): torch.stack([torch.tensor(edge_t[4]), torch.tensor(edge_s[4])]),
        ('year', 'activein_r', 'author'): torch.stack([torch.tensor(edge_t[5]), torch.tensor(edge_s[5])])
        }
    edge_index = to_undirected(edge_index_dict[('phrase', 'cooccur', 'phrase')])
    edge_index_dict[('phrase', 'cooccur', 'phrase')] = edge_index
    edge_index = to_undirected(edge_index_dict[('author', 'coauthor', 'author')])
    edge_index_dict[('author', 'coauthor', 'author')] = edge_index
    edge_index = to_undirected(edge_index_dict[('author', 'cite', 'author')])
    edge_index_dict[('author', 'cite', 'author')] = edge_index
    
    with open(path + "DBLP_link.pkl", mode="wb") as f:
        pickle.dump((edge_index_dict, feature_dict, pos_link_s, pos_link_t, test_link_s, test_link_t, flags, num_nodes_dict), f)


def get_node_id_yelp(id):
    num_business = 7474
    num_location = 39
    num_stars = 9
    num_phrase = 74943
    idx_loaction = num_business
    idx_stars = num_business + num_location
    idx_phrase = num_business + num_location + num_stars
    num_nodes = num_business + num_location + num_stars + num_phrase

    id = int(id)
    if id < num_business:
        return id
    elif id < num_business + num_location:
        return (id - idx_loaction)
    elif id < num_business + num_location + num_stars:
        return (id - idx_stars)
    else:
        return (id - idx_phrase)

def load_Yelp():
    num_business = 7474
    num_location = 39
    num_stars = 9
    num_phrase = 74943
    idx_loaction = num_business
    idx_stars = num_business + num_location
    idx_phrase = num_business + num_location + num_stars
    num_nodes = num_business + num_location + num_stars + num_phrase

    path = '../dataset/Yelp/'
    feature_file = 'features.npy'
    link_file = 'link.dat'
    newid_file = 'new_id.dat'
    link_test_file = 'link.dat.test'

    newid = np.zeros(num_nodes).astype(int)
    with open(path + newid_file) as f:
        line_data = f.readline()
        while (line_data):
            o_id, n_id = line_data.split()
            o_id = int(o_id)
            n_id = int(n_id)
            newid[o_id] = n_id
            line_data = f.readline()

    test_link_s = []
    test_link_t = []
    flags = []
    with open(path + link_test_file) as f:
        line_data = f.readline()
        while (line_data):
            s_id, t_id, flag = line_data.split()
            s_id = get_node_id_yelp(newid[int(s_id)])
            t_id = get_node_id_yelp(newid[int(t_id)])
            test_link_s.append(s_id)
            test_link_t.append(t_id)
            flags.append(int(flag))
            line_data = f.readline()

    edge_s = {}
    edge_t = {}
    for i in range(4):
        edge_s[i] = []
        edge_t[i] = []
    pos_link_s = []
    pos_link_t = []
    with open(path + link_file, encoding='utf-8') as f:
        line_data = f.readline()
        while(line_data):
            s_id, t_id, link_type, _ = line_data.split()
            s_id = get_node_id_yelp(newid[int(s_id)])
            t_id = get_node_id_yelp(newid[int(t_id)])
            if link_type == '2':
                pos_link_s.append(s_id)
                pos_link_t.append(t_id)
            edge_s[int(link_type)].append(s_id)
            edge_t[int(link_type)].append(t_id)
            line_data = f.readline()

    edge_index_dict = {
        ('business', 'locatedin', 'location'): torch.stack([torch.tensor(edge_s[0]), torch.tensor(edge_t[0])]),
        ('business', 'rate', 'stars'): torch.stack([torch.tensor(edge_s[1]), torch.tensor(edge_t[1])]),
        ('business', 'describedwith', 'phrase'): torch.stack([torch.tensor(edge_s[2]), torch.tensor(edge_t[2])]),
        ('phrase', 'context', 'phrase'): torch.stack([torch.tensor(edge_s[3]), torch.tensor(edge_t[3])]),
        ('location', 'locatedin_r', 'business'): torch.stack([torch.tensor(edge_t[0]), torch.tensor(edge_s[0])]),
        ('stars', 'rate_r', 'business'): torch.stack([torch.tensor(edge_t[1]), torch.tensor(edge_s[1])]),
        ('phrase', 'describedwith_r', 'business'): torch.stack([torch.tensor(edge_t[2]), torch.tensor(edge_s[2])]),
    }
    edge_index = to_undirected(edge_index_dict[('phrase', 'context', 'phrase')])
    edge_index_dict[('phrase', 'context', 'phrase')] = edge_index

    features_all = torch.zeros((num_nodes, 200))
    feature_temp = torch.FloatTensor(np.load(path + feature_file))
    for i in range(features_all.shape[0]):
        id_temp = newid[i]
        features_all[i] = feature_temp[id_temp]
    print("Done Feature!")
    
    features = {}
    features["business"] = features_all[:num_business]
    features["location"] = features_all[idx_loaction:idx_stars]
    features["phrase"] = features_all[idx_phrase:]
    features["stars"] = features_all[idx_stars:idx_phrase]
    num_nodes_dict = {"business": num_business, "location": num_location, "phrase": num_phrase, "stars": num_stars}
    print(len(pos_link_s), len(test_link_s))
    with open(path + "Yelp_link.pkl", mode="wb") as f:
        pickle.dump((edge_index_dict, features, pos_link_s, pos_link_t, test_link_s, test_link_t, flags, num_nodes_dict), f)


def load_Freebase():
    num_nodes = 180098

    path = "../dataset/Freebase/"
    link_file = 'link.dat'
    label_train_file = 'label.dat'
    label_test_file = 'label.dat.test'

    new_nid = torch.tensor([0 for i in range(num_nodes)])
    n_type = torch.tensor([0 for i in range(num_nodes)])
    with open(path + "node.dat", mode="r", encoding="utf-8") as f:
        for line in f:
            s = line.strip("\n").split("\t")
            n_type[int(s[0])] = int(s[2])

    num_nodes_dict = {}
    for ntype in range(8):
        mask = n_type == ntype
        new_nid[mask] = torch.tensor([i for i in range(torch.sum(mask).item())])
        num_nodes_dict[str(ntype)] = torch.sum(mask).item()
        
    new_nid = new_nid.tolist()
    labels = torch.zeros(num_nodes_dict["0"], dtype=torch.long)
    train_idx = []
    test_idx = []
    with open(path + label_train_file) as f:
        line_data = f.readline()
        while line_data:
            o_id, _, _, label = line_data.split()
            idx = new_nid[int(o_id)]
            train_idx.append(idx)
            labels[idx] = int(label)
            line_data = f.readline()
    print("read train done")
    with open(path + label_test_file) as f:
        line_data = f.readline()
        while line_data:
            o_id, _, _, label = line_data.split()
            idx = new_nid[int(o_id)]
            test_idx.append(idx)
            labels[idx] = int(label)
            line_data = f.readline()
    print("read test done")
    edge_index = {}
    with open(path + link_file, encoding='utf-8') as f:
        line_data = f.readline()
        while line_data:
            s_id, t_id, link_type, _ = line_data.split()
            s_ntype = n_type[int(s_id)].item()
            t_ntype = n_type[int(t_id)].item()
            s_id = new_nid[int(s_id)]
            t_id = new_nid[int(t_id)]
            if (str(s_ntype), str(s_ntype) + "-" + str(t_ntype), str(t_ntype)) not in edge_index.keys():
                if s_ntype != t_ntype:
                    edge_index[(str(s_ntype), str(s_ntype) + "-" + str(t_ntype), str(t_ntype))] = [[s_id], [t_id]]
                    edge_index[(str(t_ntype), str(t_ntype) + "-" + str(s_ntype), str(s_ntype))] = [[t_id], [s_id]]
                else:
                    edge_index[(str(s_ntype), str(s_ntype) + "-" + str(t_ntype), str(t_ntype))] = [[s_id, t_id], [t_id, s_id]]
            else:
                edge_index[(str(s_ntype), str(s_ntype) + "-" + str(t_ntype), str(t_ntype))][0].append(s_id)
                edge_index[(str(s_ntype), str(s_ntype) + "-" + str(t_ntype), str(t_ntype))][1].append(t_id)
                edge_index[(str(t_ntype), str(t_ntype) + "-" + str(s_ntype), str(s_ntype))][0].append(t_id)
                edge_index[(str(t_ntype), str(t_ntype) + "-" + str(s_ntype), str(s_ntype))][1].append(s_id)
            line_data = f.readline()
    print("read link done")
    edge_index_dict = {}
    for key in edge_index.keys():
        edge_index_dict[key] = torch.stack([torch.tensor(edge_index[key][0]),
                                            torch.tensor(edge_index[key][1])])
    with open(path + "Freebase.pkl", mode="wb") as f:
        pickle.dump((edge_index_dict, train_idx, test_idx, labels, num_nodes_dict), f)



if __name__ == "__main__":
    load_DBLP_link()