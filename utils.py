import dgl
import torch
import torch.utils.data as data
import time
import scipy.sparse as sp
from tqdm import tqdm
from ogb.nodeproppred import  Evaluator

class ClusterDataset(data.Dataset):
    '''
    从语义图中提取出由cluster和influential_node组成的子图，
    clusters是由元路径
    转换为dgl.DGLGraph对象，并添加mask属性，用于后续的训练和推理
    param:
        clusters: list, 每个元素是一个cluster的节点索引列表
        influential_nodes: list, 每个元素是一个influential_node的节点索引列表
        graph: dgl.DGLGraph, 原始图
        add_nodes: bool, 是否添加influential_nodes到graph中
    '''
    def __init__(self, clusters, influential_nodes, graph, add_nodes=False):
        self.clusters = clusters
        self.influential_nodes = influential_nodes
        self.graph = graph
        self.add_nodes = add_nodes

    def __len__(self):
        return len(self.clusters)
    
    def __getitem__(self, idx):
        mask = torch.tensor([True for i in range(len(self.clusters[idx]))] + 
                            [False for j in range(len(self.influential_nodes[idx]))])
        subgraph = dgl.node_subgraph(self.graph, self.clusters[idx] + self.influential_nodes[idx])
        subgraph.ndata["mask"] = mask
        return subgraph

# class ClusterHeteroGraphDataset(data.Dataset):
#     '''
#     将一个学术网络异构图划分为多个包含不同类型节点的子图，并在子图中添加训练节点的掩码
#     param:
#         clusters: list, 每个元素是一个cluster的节点索引列表
#         influential_nodes: list, 每个元素是一个influential_node的节点索引列表
#         graph: dgl.DGLHeteroGraph, 原始图
#         offset: dict, 每个类型节点的偏移量
#         node_type: dict, 每个节点类型对应的索引
#     '''
#     def __init__(self, clusters, influential_nodes, graph, offset, node_type):
#         self.clusters = clusters
#         self.influential_nodes = influential_nodes
#         self.graph = graph
#         self.offset = offset
#         self.node_type = node_type

#     def __len__(self):
#         return len(self.clusters)

#     def __getitem__(self, idx):
#         cluster = self.clusters[idx] + self.influential_nodes[idx]
#         cluster = torch.tensor(cluster)
#         nodes = {"author": [], "field": [], "institution": [], "paper": []}
#         node_type = self.node_type[cluster]
#         nodes["author"] = cluster[node_type == 0] - self.offset["author"]
#         nodes["field"] = cluster[node_type == 1] - self.offset["field"]
#         nodes["institution"] = cluster[node_type == 2] - self.offset["institution"]
#         nodes["paper"] = cluster[node_type == 3] - self.offset["paper"]
#         num_target_nodes = torch.sum(self.node_type[self.clusters[idx]] == 3)

#         subgraph = dgl.node_subgraph(self.graph, nodes)
#         mask = torch.tensor([False for i in range(subgraph.num_nodes("paper"))])
#         mask[:num_target_nodes] = True
#         subgraph.nodes["paper"].data["mask"] = mask
#         return subgraph

def cluster_by_metis(coo_matirx, num_parts):
    '''
    使用metis算法将一个图划分为多个子图
    param:
        coo_matirx: scipy.sparse.coo_matrix, 稀疏矩阵
        num_parts: int, 划分的子图数量
    return:
        clusters: list, 每个元素是一个子图的节点索引列表
    '''
    g = dgl.graph((coo_matirx.row, coo_matirx.col))
    subgraphs = dgl.metis_partition(g, num_parts).values()
    clusters = []
    for subgraph in subgraphs:
        clusters.append(subgraph.ndata[dgl.NID].tolist())
    return clusters


def get_ogb_evaluator(dataset):
    '''
    根据数据集名称获取Open Graph Benchmark的评估器
    param:
        dataset: str, 数据集名称
    return:
        evaluator: function, 评估器
    '''
    evaluator = Evaluator(name=dataset)
    return lambda preds, labels: evaluator.eval({
        "y_true": labels.view(-1, 1),
        "y_pred": preds.view(-1, 1),
    })["acc"]


def get_n_params(model):
    '''
    获取模型参数数量
    param:
        model: torch.nn.Module, 模型
    return:
        pp: int, 模型参数数量
    '''
    pp = 0
    for p in list(model.parameters()):
        nn = 1
        for s in list(p.size()):
            nn = nn * s
        pp += nn
    return pp

def cluster(node2node, num_parts, nodes, adj, idx):
    '''
    使用metis算法将一个图划分为多个子图;
    该方法的效果可与通过贪心算法分配节点的效果相媲美，并且用时更少
    param:
        node2node: list, 每个元素是一个子图的节点索引列表
        num_parts: int, 划分的子图数量
        nodes: list, 节点索引列表
        adj: scipy.sparse.coo_matrix, 稀疏矩阵
    '''
    t_1 = time.time()
    sub_mat = [[] for i in range(len(nodes) - 1)]
    for i in range(len(nodes) - 1):
        for j in range(len(nodes) - 1):
            sub_mat[i].append(adj[nodes[i]: nodes[i + 1], nodes[j]: nodes[j + 1]])
    sub_mat[idx][idx] = node2node
    new_adj = sp.bmat(sub_mat, dtype=adj.dtype)
    clusters = cluster_by_metis(new_adj.tocoo(), num_parts=num_parts)
    print("clustering costs {:.4f}s".format(time.time() - t_1))
    return clusters


def global_clusters(adj, clusters, limit, node_degree):
    '''
    将多个子图的节点映射到全局节点
    param:
        adj: scipy.sparse.coo_matrix, 稀疏矩阵
        clusters: list, 每个元素是一个子图的节点索引列表
        limit: int, 每个子图的节点数量
        node_degree: list, 每个节点的度数
    '''
    trans_matrix = sp.lil_matrix((adj.shape[0], len(clusters)))
    for i in tqdm(range(len(clusters))):
        for j in clusters[i]:
            trans_matrix[j, i] = 1
    trans_matrix = trans_matrix.tocsr()
    local2global = adj.dot(trans_matrix).toarray()
    mask = torch.ones((len(clusters), adj.shape[0]))
    for i in range(mask.shape[0]):
        mask[i][clusters[i]] = 0
    local2global = torch.mul(torch.from_numpy(local2global), mask.T)
    values_nodes, indices_nodes = local2global.topk(limit, dim=0, largest=True, sorted=True)
    return indices_nodes.T.tolist()


def accuracy(preds, labels):
    '''
    计算准确率
    param:
        preds: torch.Tensor, 预测结果
        labels: torch.Tensor, 真实标签
    return:
        acc: float, 准确率
    '''
    result = torch.tensor([preds[i] == labels[i] for i in range(preds.shape[0])])
    return (torch.sum(result) / preds.shape[0]).item()


def distributed_sum(prob):
    '''
    分布式求和
    param:
        prob: torch.Tensor, 概率
    return:
        result: torch.Tensor, 求和结果
    '''
    output_tensors = [prob.clone() for _ in range(torch.distributed.get_world_size())]
    torch.distributed.all_gather(output_tensors, prob)
    result= sum(output_tensors)
    # 截断由SequentialDistributedSampler添加的虚拟元素
    return result