import argparse
import os
import pickle
import random
import warnings
import scipy.sparse as sp
import dgl
import dgl.function as fn
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from tqdm import tqdm
import time
from sklearn.metrics import roc_auc_score, average_precision_score
from models import NARS, SeHGNN
from torch.utils.data import Dataset
import torch.distributed as dist
from utils import get_n_params, distributed_sum, cluster, global_clusters, ClusterDataset


warnings.filterwarnings("ignore")
parser = argparse.ArgumentParser(description='Yelp Link Prediction with NARS and SeHGNN')
parser.add_argument('--device', type=int, default=0)
parser.add_argument('--num_layer', type=int, default=3)
parser.add_argument('--hidden_channel', type=int, default=256)  # As specified: hidden layer size is 256
parser.add_argument('--dropout', type=float, default=0.1)       # As specified: sigma is 0.1 for Yelp
parser.add_argument('--lr', type=float, default=0.001)
parser.add_argument('--epochs', type=int, default=300)
parser.add_argument('--batch_size', type=int, default=1000)     # As specified: batch size is 1000
parser.add_argument('--runs', type=int, default=1)
parser.add_argument('--save_path', type=str, default="../partition", help="Partition save path")
parser.add_argument('--model', type=str, default="nars", help="Model selection: nars or sehgnn")
parser.add_argument('--local_rank', type=int, default=0, help="Local rank for distributed training")
parser.add_argument('--num_parts', type=int, default=100, help="Number of partitions")
args = parser.parse_args()
print(args)


class Mydataset(Dataset):
    def __init__(self, subgraphs):
        self.train = subgraphs
        self.len = len(subgraphs)
 
    def __getitem__(self, item):
        return self.train[item]
 
    def __len__(self):
        return self.len


def load_Yelp_link():
    """
    Load Yelp dataset for link prediction task between businesses and phrases
    """
    path = "../dataset/Yelp/"
    with open(path + "Yelp_link.pkl", mode="rb") as f:
        edge_index_dict, features, pos_link_s, pos_link_t, test_link_s, test_link_t, flags, num_nodes_dict = pickle.load(f)

    # Create heterogeneous graph
    g = dgl.heterograph(
        {k: (edge_index_dict[k][0], edge_index_dict[k][1]) for k in edge_index_dict.keys()},
        num_nodes_dict=num_nodes_dict
    )
    
    # Set node features
    for node_type, node_features in features.items():
        g.nodes[node_type].data["feat"] = node_features
    
    # Convert to homogeneous graph
    graph = dgl.to_homogeneous(g, ndata=["feat"])
    
    # Get node features
    feat = graph.ndata.pop("feat")
    
    print(f"# Nodes: {graph.number_of_nodes()}\n"
          f"# Edges: {graph.number_of_edges()}")
    
    # Create positive edges for training and testing
    train_pos_edges = torch.stack([torch.tensor(pos_link_s), torch.tensor(pos_link_t)])
    test_pos_edges = torch.stack([torch.tensor(test_link_s), torch.tensor(test_link_t)])
    
    # Create mask for positive test edges
    test_pos_mask = torch.tensor(flags) == 1
    test_neg_mask = torch.tensor(flags) == 0
    
    # Get positive and negative test edges
    test_pos_edges = test_pos_edges[:, test_pos_mask]
    test_neg_edges = torch.stack([torch.tensor(test_link_s), torch.tensor(test_link_t)])
    test_neg_edges = test_neg_edges[:, test_neg_mask]
    
    return graph, feat, num_nodes_dict, train_pos_edges, test_pos_edges, test_neg_edges


def generate_neg_samples(graph, pos_edges, num_samples, node_type_offset=None):
    """
    Generate negative samples (edges that don't exist in the graph)
    Focuses on Business-Phrase relationships
    """
    num_nodes = graph.number_of_nodes()
    
    if node_type_offset is not None:
        # Generate negative edges between business and phrase nodes
        business_start = 0
        business_end = node_type_offset["location"]
        phrase_start = node_type_offset["stars"] + node_type_offset["stars"]
        phrase_end = phrase_start + node_type_offset["phrase"]
        
        # Generate random business-phrase edges
        neg_src = torch.randint(business_start, business_end, (num_samples,))
        neg_dst = torch.randint(phrase_start, phrase_end, (num_samples,))
    else:
        # Generate random edges
        neg_src = torch.randint(0, num_nodes, (num_samples,))
        neg_dst = torch.randint(0, num_nodes, (num_samples,))
    
    # Check if the edges already exist in the positive edges
    existing_edges = {(pos_edges[0, i].item(), pos_edges[1, i].item()) for i in range(pos_edges.shape[1])}
    valid_indices = []
    
    for i in range(num_samples):
        if (neg_src[i].item(), neg_dst[i].item()) not in existing_edges:
            valid_indices.append(i)
    
    # If we have fewer valid edges than needed, generate more
    while len(valid_indices) < num_samples:
        if node_type_offset is not None:
            new_src = torch.randint(business_start, business_end, (num_samples,))
            new_dst = torch.randint(phrase_start, phrase_end, (num_samples,))
        else:
            new_src = torch.randint(0, num_nodes, (num_samples,))
            new_dst = torch.randint(0, num_nodes, (num_samples,))
        
        for i in range(num_samples):
            if len(valid_indices) >= num_samples:
                break
            if (new_src[i].item(), new_dst[i].item()) not in existing_edges:
                neg_src = torch.cat([neg_src, new_src[i].unsqueeze(0)])
                neg_dst = torch.cat([neg_dst, new_dst[i].unsqueeze(0)])
                valid_indices.append(len(neg_src) - 1)
    
    # Select only valid edges
    neg_src = neg_src[valid_indices[:num_samples]]
    neg_dst = neg_dst[valid_indices[:num_samples]]
    
    return torch.stack([neg_src, neg_dst])


def prepare_link_prediction_data(graph, train_pos_edges, test_pos_edges, test_neg_edges, num_train_edges=250000, num_test_edges=750000, num_neg_train_samples=25000, node_type_offset=None):
    """
    Prepare data for link prediction
    """
    # Sample train positive edges if needed
    if train_pos_edges.size(1) > num_train_edges:
        perm = torch.randperm(train_pos_edges.size(1))[:num_train_edges]
        train_pos_edges = train_pos_edges[:, perm]
    
    # Sample test positive edges if needed
    if test_pos_edges.size(1) > num_test_edges // 2:
        perm = torch.randperm(test_pos_edges.size(1))[:num_test_edges // 2]
        test_pos_edges = test_pos_edges[:, perm]
    
    # Sample test negative edges if needed
    if test_neg_edges.size(1) > num_test_edges // 2:
        perm = torch.randperm(test_neg_edges.size(1))[:num_test_edges // 2]
        test_neg_edges = test_neg_edges[:, perm]
    
    # Generate negative training edges
    train_neg_edges = generate_neg_samples(graph, train_pos_edges, num_neg_train_samples, node_type_offset)
    
    # Create training graph with positive edges
    train_graph = dgl.graph((train_pos_edges[0], train_pos_edges[1]), num_nodes=graph.number_of_nodes())
    
    # Copy edge types from original graph
    if "_TYPE" in graph.edata:
        train_graph.edata["_TYPE"] = graph.edata["_TYPE"][:train_pos_edges.size(1)]
    else:
        train_graph.edata["_TYPE"] = torch.zeros(train_pos_edges.size(1), dtype=torch.long)
    
    return train_graph, train_pos_edges, train_neg_edges, test_pos_edges, test_neg_edges


class LinkPredictor(nn.Module):
    def __init__(self, in_channels, hidden_channels):
        super(LinkPredictor, self).__init__()
        self.lin1 = nn.Linear(in_channels * 2, hidden_channels)
        self.lin2 = nn.Linear(hidden_channels, 1)
    
    def forward(self, x_i, x_j):
        x = torch.cat([x_i, x_j], dim=1)
        x = self.lin1(x)
        x = F.relu(x)
        x = F.dropout(x, p=0.1, training=self.training)
        x = self.lin2(x)
        return torch.sigmoid(x)


def extract_metapaths(graph, num_nodes_dict):
    """
    Extract metapaths:
    - PP (Phrase-Phrase)
    - BSB (Business-Star-Business)
    - BLB (Business-Location-Business)
    - BPB (Business-Phrase-Business)
    """
    # Get node type offsets
    business_offset = 0
    location_offset = business_offset + num_nodes_dict["business"]
    stars_offset = location_offset + num_nodes_dict["location"]
    phrase_offset = stars_offset + num_nodes_dict["stars"]
    
    # Convert to scipy sparse matrix
    adj = graph.adj(scipy_fmt='csr')
    adj = adj.tolil()
    
    # Extract PP metapath (Phrase-Phrase)
    pp = adj[phrase_offset:phrase_offset+num_nodes_dict["phrase"],
           phrase_offset:phrase_offset+num_nodes_dict["phrase"]]
    
    # Extract B-S (Business-Star) and S-B (Star-Business) connections
    bs = adj[business_offset:business_offset+num_nodes_dict["business"],
           stars_offset:stars_offset+num_nodes_dict["stars"]]
    sb = bs.T
    
    # Extract B-L (Business-Location) and L-B (Location-Business) connections
    bl = adj[business_offset:business_offset+num_nodes_dict["business"],
           location_offset:location_offset+num_nodes_dict["location"]]
    lb = bl.T
    
    # Extract B-P (Business-Phrase) and P-B (Phrase-Business) connections
    bp = adj[business_offset:business_offset+num_nodes_dict["business"],
           phrase_offset:phrase_offset+num_nodes_dict["phrase"]]
    pb = bp.T
    
    # Construct metapaths
    bsb = bs.dot(sb)  # Business-Star-Business
    blb = bl.dot(lb)  # Business-Location-Business
    bpb = bp.dot(pb)  # Business-Phrase-Business
    
    return pp, bsb, blb, bpb


def train(model, predictor, subgraphs, train_pos_edges, train_neg_edges, optimizer, feat, batch_size=1000, device='cuda'):
    """
    Train the model using metapath-based subgraphs
    """
    model.train()
    predictor.train()
    
    # Prepare positive and negative edge data
    train_edges = torch.cat([train_pos_edges, train_neg_edges], dim=1)
    train_labels = torch.zeros(train_edges.shape[1])
    train_labels[:train_pos_edges.shape[1]] = 1.0
    
    total_loss = 0
    # Process in batches of subgraphs
    for batch in subgraphs:
        # Move batch to device
        batch = batch.to(device)
        nid = batch.ndata[dgl.NID]
        
        # Forward pass through the model
        node_embeddings = model(batch, feat[nid].to(device), torch.zeros((nid.shape[0], model.out_feats)).to(device))
        
        # Process in batches of edges
        batch_losses = []
        for start in range(0, train_edges.shape[1], batch_size):
            end = min(start + batch_size, train_edges.shape[1])
            batch_edges = train_edges[:, start:end]
            batch_labels = train_labels[start:end].to(device)
            
            # Map global node IDs to local batch IDs
            batch_nid_map = {nid[i].item(): i for i in range(len(nid))}
            mask = torch.tensor([
                (batch_edges[0, i].item() in batch_nid_map) and (batch_edges[1, i].item() in batch_nid_map)
                for i in range(batch_edges.shape[1])
            ])
            
            # Skip if no edges in this batch are in the current subgraph
            if not mask.any():
                continue
                
            batch_edges_local = batch_edges[:, mask]
            batch_labels_local = batch_labels[mask]
            
            # Map global node IDs to local IDs in the batch
            src_local = torch.tensor([batch_nid_map[src.item()] for src in batch_edges_local[0]]).to(device)
            dst_local = torch.tensor([batch_nid_map[dst.item()] for dst in batch_edges_local[1]]).to(device)
            
            # Get embeddings for edges
            src_embeds = node_embeddings[src_local]
            dst_embeds = node_embeddings[dst_local]
            
            # Predict and calculate loss
            pred = predictor(src_embeds, dst_embeds).squeeze()
            loss = F.binary_cross_entropy(pred, batch_labels_local)
            batch_losses.append(loss)
            
        # Skip if no edges processed in this subgraph
        if not batch_losses:
            continue
            
        # Calculate average loss for all batches
        loss = torch.mean(torch.stack(batch_losses))
        
        # Backward pass
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item()
    
    return total_loss / max(len(subgraphs), 1)  # Avoid division by zero if there are no subgraphs


@torch.no_grad()
def test(model, predictor, test_subgraphs, test_pos_edges, test_neg_edges, feat, batch_size=1000, device='cuda'):
    """
    Test the model on the test data
    """
    model.eval()
    predictor.eval()
    
    # Combine positive and negative edges for evaluation
    test_edges = torch.cat([test_pos_edges, test_neg_edges], dim=1)
    test_labels = torch.zeros(test_edges.shape[1])
    test_labels[:test_pos_edges.shape[1]] = 1.0
    
    # Process all subgraphs to get node embeddings
    node_embeddings_dict = {}
    for batch in test_subgraphs:
        batch = batch.to(device)
        nid = batch.ndata[dgl.NID]
        
        # Get embeddings
        node_emb = model(batch, feat[nid].to(device), torch.zeros((nid.shape[0], model.out_feats)).to(device))
        
        # Store embeddings by node ID
        for i, nid_val in enumerate(nid.tolist()):
            node_embeddings_dict[nid_val] = node_emb[i].cpu()
    
    # Convert node embeddings dictionary to tensor
    all_node_ids = torch.arange(feat.shape[0])
    all_node_embeddings = torch.zeros((feat.shape[0], model.out_feats))
    for nid, emb in node_embeddings_dict.items():
        all_node_embeddings[nid] = emb
    
    # Process in batches of edges
    y_pred = []
    edge_mask = []  # Track which edges have embeddings
    
    for start in range(0, test_edges.shape[1], batch_size):
        end = min(start + batch_size, test_edges.shape[1])
        batch_edges = test_edges[:, start:end]
        
        # Check if we have embeddings for both source and target nodes
        batch_mask = torch.tensor([
            (batch_edges[0, i].item() in node_embeddings_dict) and (batch_edges[1, i].item() in node_embeddings_dict)
            for i in range(batch_edges.shape[1])
        ])
        
        edge_mask.append(batch_mask)
        
        if not batch_mask.any():
            continue
        
        # Get embeddings for edges with available embeddings
        src_edges = batch_edges[0, batch_mask]
        dst_edges = batch_edges[1, batch_mask]
        
        src_embeds = torch.stack([node_embeddings_dict[src.item()] for src in src_edges])
        dst_embeds = torch.stack([node_embeddings_dict[dst.item()] for dst in dst_edges])
        
        # Predict
        pred = predictor(src_embeds.to(device), dst_embeds.to(device)).squeeze().cpu()
        y_pred.append(pred)
    
    # Combine edge masks
    if not edge_mask:
        return 0.5, 0.5
    
    edge_mask = torch.cat(edge_mask, dim=0)
    
    # Only evaluate on edges for which we have embeddings
    if not y_pred:  # If no predictions were made
        return 0.5, 0.5  # Return default metrics
    
    y_pred = torch.cat(y_pred, dim=0).numpy()
    y_true = test_labels[edge_mask].numpy()
    
    # Calculate evaluation metrics
    auc = roc_auc_score(y_true, y_pred)
    ap = average_precision_score(y_true, y_pred)
    
    return auc, ap


def main():
    # Set device
    device = f'cuda:{args.device}' if torch.cuda.is_available() else 'cpu'
    
    start_time = time.time()
    
    # Load Yelp dataset for link prediction
    graph, feat, num_nodes_dict, train_pos_edges, test_pos_edges, test_neg_edges = load_Yelp_link()
    print(f"Loading data took {time.time() - start_time:.4f}s")
    
    # Create node type offsets
    node_type_offset = {}
    offset = 0
    for ntype, count in num_nodes_dict.items():
        node_type_offset[ntype] = offset
        offset += count
    
    # Prepare data for link prediction
    train_graph, train_pos_edges, train_neg_edges, test_pos_edges, test_neg_edges = prepare_link_prediction_data(
        graph, train_pos_edges, test_pos_edges, test_neg_edges,
        num_train_edges=250000,      # As specified: 250k edges for training
        num_test_edges=750000,       # As specified: 750k edges for testing
        num_neg_train_samples=25000, # As specified: 25k negative samples for training
        node_type_offset=node_type_offset
    )
    
    # Extract metapaths
    limit = graph.num_nodes() // args.num_parts // 10
    
    # Define partition path
    partition_path = os.path.join(args.save_path, f"Yelp-{args.model}-partition{args.num_parts}.pkl")
    
    # Create partitions based on metapaths if they don't exist
    if not os.path.exists(partition_path):
        t_1 = time.time()
        
        # Extract adjacency matrix and metapaths
        pp, bsb, blb, bpb = extract_metapaths(graph, num_nodes_dict)
        
        # Create clusters for metapaths
        pp_clusters = cluster(pp, args.num_parts, 
                            [0, node_type_offset["location"], node_type_offset["stars"] + node_type_offset["stars"]],
                            graph.adj(scipy_fmt='csr'), 0)
        
        bsb_clusters = cluster(bsb, args.num_parts,
                             [0, node_type_offset["location"], node_type_offset["stars"] + node_type_offset["stars"]],
                             graph.adj(scipy_fmt='csr'), 1)
        
        blb_clusters = cluster(blb, args.num_parts,
                             [0, node_type_offset["location"], node_type_offset["stars"] + node_type_offset["stars"]],
                             graph.adj(scipy_fmt='csr'), 1)
        
        bpb_clusters = cluster(bpb, args.num_parts,
                             [0, node_type_offset["location"], node_type_offset["stars"] + node_type_offset["stars"]],
                             graph.adj(scipy_fmt='csr'), 1)
        
        print(f"Number of nodes in each cluster: {len(pp_clusters[0])}")
        
        # Get influential nodes for each cluster
        pp_influential_nodes = global_clusters(graph.adj(scipy_fmt='csr'), pp_clusters, limit, graph.in_degrees())
        bsb_influential_nodes = global_clusters(graph.adj(scipy_fmt='csr'), bsb_clusters, limit, graph.in_degrees())
        blb_influential_nodes = global_clusters(graph.adj(scipy_fmt='csr'), blb_clusters, limit, graph.in_degrees())
        bpb_influential_nodes = global_clusters(graph.adj(scipy_fmt='csr'), bpb_clusters, limit, graph.in_degrees())
        
        print(f"Number of influential nodes in each cluster: {len(pp_influential_nodes[0])}")
        print(f"Partitioning time: {time.time() - t_1:.4f}s")
        
        # Save partitions
        with open(partition_path, mode='wb') as f:
            pickle.dump((pp_clusters, bsb_clusters, blb_clusters, bpb_clusters,
                       pp_influential_nodes, bsb_influential_nodes, blb_influential_nodes, bpb_influential_nodes), f)
    else:
        # Load existing partitions
        with open(partition_path, mode='rb') as f:
            pp_clusters, bsb_clusters, blb_clusters, bpb_clusters, \
            pp_influential_nodes, bsb_influential_nodes, blb_influential_nodes, bpb_influential_nodes = pickle.load(f)
    
    # Get edge types
    num_etypes = train_graph.edata.get("_TYPE", torch.zeros(train_graph.number_of_edges(), dtype=torch.long)).max().item() + 1
    
    # Prepare metapaths for modeling
    metapaths = ["pp", "bsb", "blb", "bpb"]
    clusters = {
        "pp": pp_clusters, 
        "bsb": bsb_clusters, 
        "blb": blb_clusters, 
        "bpb": bpb_clusters
    }
    influential_nodes = {
        "pp": pp_influential_nodes, 
        "bsb": bsb_influential_nodes, 
        "blb": blb_influential_nodes, 
        "bpb": bpb_influential_nodes
    }
    
    # Create cluster datasets
    cluster_data = [
        ClusterDataset(clusters[metapaths[0]], influential_nodes[metapaths[0]], graph),
        ClusterDataset(clusters[metapaths[1]], influential_nodes[metapaths[1]], graph),
        ClusterDataset(clusters[metapaths[2]], influential_nodes[metapaths[2]], graph),
        ClusterDataset(clusters[metapaths[3]], influential_nodes[metapaths[3]], graph)
    ]
    
    # Create subgraphs for training
    subgraphs = []
    for i in range(args.num_parts):
        for dataset in cluster_data:
            subgraphs.append(dataset[i])
        
    dataset = Mydataset(subgraphs)
    train_loader = dgl.dataloading.GraphDataLoader(dataset, batch_size=2, drop_last=False, num_workers=4)
    
    # Create test subgraphs
    test_subgraphs = []
    for i in range(len(cluster_data)):
        for j in range(args.num_parts):
            test_subgraphs.append(cluster_data[i][j])
    
    test_dataset = Mydataset(test_subgraphs)
    test_loader = dgl.dataloading.GraphDataLoader(test_dataset, batch_size=8, drop_last=False, num_workers=4)
    
    # Create model
    in_feats = feat.shape[1]
    
    for r in range(args.runs):
        # Initialize model based on choice
        if args.model == "nars":
            model = NARS(in_feats, args.hidden_channel, args.hidden_channel, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        else:  # sehgnn
            model = SeHGNN(in_feats, args.hidden_channel, args.hidden_channel, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        
        model = model.to(device)
        print(f"Model: {args.model}")
        print("# Params:", get_n_params(model))
        
        # Initialize link predictor
        predictor = LinkPredictor(args.hidden_channel, args.hidden_channel // 2).to(device)
        
        # Initialize optimizer
        optimizer = torch.optim.Adam(
            list(model.parameters()) + list(predictor.parameters()), 
            lr=args.lr
        )
        
        # Training
        best_auc = 0
        for epoch in range(1, args.epochs + 1):
            t1 = time.time()
            
            # Train
            loss = train(
                model, predictor, train_loader, train_pos_edges, train_neg_edges, 
                optimizer, feat, args.batch_size, device
            )
            
            t2 = time.time()
            
            # Test
            auc, ap = test(
                model, predictor, test_loader, test_pos_edges, test_neg_edges, 
                feat, args.batch_size, device
            )
            
            t3 = time.time()
            
            # Update best results
            if auc > best_auc:
                best_auc = auc
            
            # Print log
            log = f"Epoch {epoch}, Loss: {loss:.4f}, AUC: {auc:.4f}, AP: {ap:.4f}"
            log += f", Training Time(s): {t2 - t1:.4f}, Inference Time(s): {t3 - t2:.4f}"
            print(log)
        
        print(f"Best AUC: {best_auc:.4f}")
    
    print(f"Total time: {time.time() - start_time:.4f}s")


if __name__ == "__main__":
    main()
