import os
import argparse
from complex_emb import process_dataset

def main():
    parser = argparse.ArgumentParser(description='Process datasets using ComplEx model')
    parser.add_argument('--data_dir', type=str, default='../dataset', help='Directory containing dataset files')
    parser.add_argument('--output_dir', type=str, default='../embeddings', help='Directory to save generated embeddings')
    parser.add_argument('--datasets', type=str, nargs='+', default=None, 
                        help='Specific datasets to process (default: all)')
    parser.add_argument('--dim', type=int, default=200, help='Embedding dimension')
    parser.add_argument('--lr', type=float, default=0.001, help='Learning rate')
    parser.add_argument('--epochs', type=int, default=100, help='Number of training epochs')
    parser.add_argument('--margin', type=float, default=1.0, help='Margin for ranking loss')
    parser.add_argument('--num_neg', type=int, default=5, help='Number of negative samples per positive sample')
    parser.add_argument('--gpu', type=int, default=0, help='GPU device ID, -1 for CPU')
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 默认处理所有5个数据集
    all_datasets = ['DBLP', 'Yelp', 'Pubmed', 'Freebase', 'ogbn-mag']
    datasets_to_process = args.datasets if args.datasets else all_datasets
    
    print(f"Will process the following datasets: {datasets_to_process}")
    
    # 处理每个数据集
    for dataset_name in datasets_to_process:
        if dataset_name not in all_datasets:
            print(f"Warning: Unknown dataset {dataset_name}, skipping...")
            continue
        
        print(f"\n{'='*50}")
        print(f"Processing dataset: {dataset_name}")
        print(f"{'='*50}")
        
        process_dataset(dataset_name, args)
        
        print(f"{'='*50}")
        print(f"Finished processing dataset: {dataset_name}")
        print(f"{'='*50}\n")
    
    print("All datasets processed successfully!")

if __name__ == "__main__":
    main() 