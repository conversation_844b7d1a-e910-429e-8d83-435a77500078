import argparse
import os
import pickle
import random
import warnings
import scipy.sparse as sp
import dgl
from ogb.nodeproppred import DglNodePropPredDataset
from utils import *
from models import *
import torch.nn.functional as F
import dgl.function as fn
import numpy as np
import torch
import time
from tqdm import tqdm
import warnings
from torch.utils.data import Dataset
import torch.distributed as dist
from torch.utils.data.distributed import DistributedSampler

warnings.filterwarnings("ignore")
parser = argparse.ArgumentParser(description='OGBN-MAG (Cluster-RGNN)')
parser.add_argument('--local_rank', type=int, help=".")
parser.add_argument('--device', type=int, default=0)
parser.add_argument('--num_layer', type=int, default=3)
parser.add_argument('--hidden_channel', type=int, default=512)
parser.add_argument('--dropout', type=float, default=0.5)
parser.add_argument('--lr', type=float, default=0.001)
parser.add_argument('--epochs', type=int, default=300)
parser.add_argument('--batch_size', type=int, default=25000)
parser.add_argument('--runs', type=int, default=1)
parser.add_argument('--save_path', type=str, default="../partition", help=".")
parser.add_argument('--dataset', type=str, default="ogbn-mag", help=".")
parser.add_argument('--num_parts', type=int, default=100, help=".")
parser.add_argument('--mask', type=float, default=0.5, help=".")
parser.add_argument('--supply-rate', type=float, default=0.1, help=".")
parser.add_argument('--use_emb', action='store_true', default=False, help=".")
parser.add_argument('--model', type=str, default="rgat", help=".")
args = parser.parse_args()
print(args)
dist.init_process_group(backend='nccl')
torch.cuda.set_device(args.local_rank)

class Mydataset(Dataset):
    def __init__(self, subgraphs):
        self.train = subgraphs
        self.len = len(subgraphs)
 
    def __getitem__(self, item):
        return self.train[item]
 
    def __len__(self):
        return self.len


def load_mag(name, device):
    """
    Load dataset and move graph and features to device
    """
    num_nodes_dict = {"author": 1134649, "field_of_study": 59965, "institution": 8740, "paper": 736389}
    author_offset = 0
    field_of_study_offset = author_offset + num_nodes_dict["author"]
    institution_offset = field_of_study_offset + num_nodes_dict["field_of_study"]
    paper_offset = institution_offset + num_nodes_dict["institution"]
    offset = {"author": author_offset, "field_of_study": field_of_study_offset,
              "institution": institution_offset, "paper": paper_offset}
    dataset = DglNodePropPredDataset(name=name, root="../dataset/")
    splitted_idx = dataset.get_idx_split()
    graph, labels = dataset[0]
    train_idx = splitted_idx["train"]["paper"]
    val_idx = splitted_idx["valid"]["paper"]
    test_idx = splitted_idx["test"]["paper"]
    # labels = labels["paper"].squeeze()
    if args.use_emb:
        author_emb = torch.load("../dataset/ogbn_mag/author.pt").float()
        field_emb = torch.load("../dataset/ogbn_mag/field_of_study.pt").float()
        institution_emb = torch.load("../dataset/ogbn_mag/institution.pt").float()
        paper_emb = torch.load("../dataset/ogbn_mag/paper.pt").float()
    else:
        src_writes, dst_writes = graph.all_edges(etype="writes")
        src_topic, dst_topic = graph.all_edges(etype="has_topic")
        src_aff, dst_aff = graph.all_edges(etype="affiliated_with")
        new_g = dgl.heterograph({
            ("paper", "written", "author"): (dst_writes, src_writes),
            ("paper", "has_topic", "field"): (src_topic, dst_topic),
            ("author", "aff", "inst"): (src_aff, dst_aff)
        })
        new_g = new_g.to(device)
        new_g.nodes["paper"].data["feat"] = graph.nodes["paper"].data["feat"].to(device)
        new_g["written"].update_all(fn.copy_u("feat", "m"), fn.mean("m", "feat"))
        new_g["has_topic"].update_all(fn.copy_u("feat", "m"), fn.mean("m", "feat"))
        new_g["aff"].update_all(fn.copy_u("feat", "m"), fn.mean("m", "feat"))
        new_g = new_g.cpu()
        author_emb = new_g.nodes["author"].data["feat"]
        field_emb = new_g.nodes["field"].data["feat"]
        institution_emb = new_g.nodes["inst"].data["feat"]
        paper_emb = graph.nodes["paper"].data["feat"]
    src_writes, dst_writes = graph.all_edges(etype="writes")
    src_topic, dst_topic = graph.all_edges(etype="has_topic")
    src_aff, dst_aff = graph.all_edges(etype="affiliated_with")
    src_cite, dst_cite = graph.all_edges(etype="cites")
    graph = dgl.heterograph({
            ("paper", "written", "author"): (dst_writes, src_writes),
            ("author", "writes", "paper"): (src_writes, dst_writes),
            ("paper", "has_topic", "field"): (src_topic, dst_topic),
            ("field", "had_topic", "paper"): (dst_topic, src_topic),
            ("author", "aff", "institution"): (src_aff, dst_aff),
            ("institution", "affed", "author"): (dst_aff, src_aff),
            ("paper", "cites", "paper"): (src_cite, dst_cite),
            ("paper", "cited", "paper"): (dst_cite, src_cite)
        })
    graph.nodes["author"].data["feat"] = author_emb
    graph.nodes["field"].data["feat"] = field_emb
    graph.nodes["institution"].data["feat"] = institution_emb
    graph.nodes["paper"].data["feat"] = paper_emb
    target_type_id = graph.get_ntype_id("paper")
    graph = dgl.to_homogeneous(graph, ndata=["feat"])
    # graph = dgl.add_reverse_edges(graph, copy_ndata=True, copy_edata=True)
    graph.ndata["target_mask"] = graph.ndata[dgl.NTYPE] == target_type_id
    train_mask = torch.tensor([False for i in range(graph.num_nodes())])
    train_mask[train_idx + paper_offset] = True
    # graph.ndata["train_mask"] = train_mask.unsqueeze(1)
    graph.ndata["train_mask"] = train_mask
    label = torch.zeros((graph.num_nodes(), 1)) - 1
    label[paper_offset:] = labels["paper"]

    n_classes = dataset.num_classes
    evaluator = get_ogb_evaluator(name)
    feat = graph.ndata.pop("feat")
    
    print(f"# Nodes: {graph.number_of_nodes()}\n"
          f"# Edges: {graph.number_of_edges()}\n"
          f"# Train: {len(train_idx)}\n"
          f"# Val: {len(val_idx)}\n"
          f"# Test: {len(test_idx)}\n"
          f"# Classes: {n_classes}")

    return graph, label.long().squeeze(), n_classes, train_idx, val_idx, test_idx, evaluator, num_nodes_dict, feat


def training(subgraphs, test_subgraphs, num_etypes, feat, args, num_classes, labels, num_nodes_dict, paper_offset, train_idx, val_idx, test_idx, label_emb):
    in_feats = feat.shape[-1]

    best_result = []
    for r in range(args.runs):
        if args.model == "rgat":
            model = RGAT(in_feats, args.hidden_channel, num_classes, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        elif args.model == "rgcn":
            model = RGCN(in_feats, args.hidden_channel, num_classes, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        elif args.model == "rgsn":
            model = RGSN(in_feats, args.hidden_channel, num_classes, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        elif args.model == "rhgnn":
            model = RHGNN(in_feats, args.hidden_channel, num_classes, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        elif args.model == "nars":
            model = NARS(in_feats, args.hidden_channel, num_classes, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        elif args.model == "sehgnn":
            model = SeHGNN(in_feats, args.hidden_channel, num_classes, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        
        # model = model.to(device)
        print(model)
        model = model.cuda()
        model = torch.nn.parallel.DistributedDataParallel(model, device_ids=[args.local_rank], find_unused_parameters=True)
        print("# Params:", get_n_params(model))
        optimizer = torch.optim.Adam(model.parameters(), lr=args.lr,
                                     weight_decay=0.0)
        print(model.device_ids[0])
        device = f'cuda:{model.device_ids[0]}' if torch.cuda.is_available() else 'cpu'
        
        best_epoch = 0
        best_val = 0
        best_test = 0
        for epoch in range(1, args.epochs + 1):
            t1 = time.time()
            model.train()
            for batch in subgraphs:
                train_mask = batch.ndata["train_mask"] * batch.ndata["mask"]
                nid = batch.ndata[dgl.NID]
                batch = batch.to(device)
                loss = F.cross_entropy(model(batch, feat[nid].cuda(), label_emb[nid].cuda())[train_mask],
                                       labels[nid][train_mask].cuda())
                optimizer.zero_grad()
                # with amp.scale_loss(loss, optimizer) as scaled_loss:
                #     scaled_loss.backward()
                loss.backward()
                optimizer.step()
            t2 = time.time()
            if epoch % 1 == 0:
                model.eval()
                probs = torch.zeros((num_nodes_dict["paper"], num_classes)).cuda()
                for batch in test_subgraphs:
                    target_mask = batch.ndata["target_mask"] * batch.ndata["mask"]
                    nid = batch.ndata[dgl.NID]
                    batch = batch.to(device)
                    probs[nid[target_mask] - paper_offset] += torch.softmax(
                            model(batch, feat[nid].cuda(),
                                         label_emb[nid].cuda())[target_mask], dim=-1).detach()
                all_probs = distributed_sum(probs).cpu()
                # all_probs = probs
                preds = torch.argmax(all_probs, dim=-1)
                train_res = accuracy(preds[train_idx], labels[paper_offset:][train_idx])
                val_res = accuracy(preds[val_idx], labels[paper_offset:][val_idx])
                test_res = accuracy(preds[test_idx], labels[paper_offset:][test_idx])
                t3 = time.time()
                if args.local_rank == 0:
                    log = "Epoch {}, Training Time(s): {:.4f}, Inference Time(s): {:.4f},".format(epoch, t2 - t1, t3 - t2)
                    log += "Acc: Train {:.4f}, Val {:.4f}, Test {:.4f}".format(train_res, val_res, test_res)
                    print(log)
                    if val_res > best_val:
                        best_epoch = epoch
                        best_val = val_res
                        best_test = test_res
                    print("Best Epoch {}, Valid {:.4f}, Test {:.4f}".format(
                            best_epoch, best_val, best_test))
        best_result.append([best_val, best_test])
        if args.local_rank == 0:
            print("Best Epoch {}, Valid {:.4f}, Test {:.4f}".format(
                best_epoch, best_val, best_test))
    if args.local_rank == 0:
        print(best_result)
        best_result = np.array(best_result)
        print("average, val {:.4f}, test {:.4f}".format(np.mean(best_result[:, 0]), np.mean(best_result[:, 1])))


if __name__ == "__main__":
    start = time.time()
    # device = f'cuda:{args.device}' if torch.cuda.is_available() else 'cpu'
    graph, labels, num_classes, train_idx, val_idx, test_idx, evaluator, num_nodes_dict, feat = load_mag(args.dataset,
                                                                                                   None)
    print("loading data costs {:.4f}s".format(time.time() - start))
    author_offset = 0
    field_of_study_offset = author_offset + num_nodes_dict["author"]
    institution_offset = field_of_study_offset + num_nodes_dict["field_of_study"]
    paper_offset = institution_offset + num_nodes_dict["institution"]
    node_offset = torch.tensor([author_offset for i in range(num_nodes_dict["author"])] +
                               [field_of_study_offset for i in range(num_nodes_dict["field_of_study"])] +
                               [institution_offset for i in range(num_nodes_dict["institution"])] +
                               [paper_offset for i in range(num_nodes_dict["paper"])])
    limit = int(graph.num_nodes() // args.num_parts * args.supply_rate)
    if not os.path.exists(args.save_path + f"/mag-{args.model}-partition{args.num_parts}.pkl"):
        t_1 = time.time()
        adj = graph.adj(scipy_fmt='csr')
        adj = adj.tolil()
        influential_nodes = torch.argsort(graph.in_degrees(),
                                          descending=True).tolist()[:limit]
        # mag: author, field_of_study, institution, paper
        p2p = adj[paper_offset: paper_offset + num_nodes_dict["paper"],
              paper_offset: paper_offset + num_nodes_dict["paper"]]

        ppp = p2p.dot(p2p.T)
        ap = adj[:num_nodes_dict["author"],
             paper_offset: paper_offset + num_nodes_dict["paper"]]
        pap = ap.T.dot(ap)
        apa = ap.dot(ap.T)
        fp = adj[field_of_study_offset: field_of_study_offset + num_nodes_dict["field_of_study"],
              paper_offset: paper_offset + num_nodes_dict["paper"]]
        fpf = fp.dot(fp.T)
        ai = adj[:num_nodes_dict["author"],
             institution_offset: institution_offset + num_nodes_dict["institution"]]
        iai = ai.T.dot(ai)
        p2p_clusters = cluster(p2p, args.num_parts,
                               [0, paper_offset, paper_offset + num_nodes_dict["paper"]],
                               adj, 1)
        pap_clusters = cluster(pap, args.num_parts,
                               [0, paper_offset, paper_offset + num_nodes_dict["paper"]],
                               adj, 1)
        ppp_clusters = cluster(ppp, args.num_parts,
                               [0, paper_offset, paper_offset + num_nodes_dict["paper"]],
                               adj, 1)

        iai_clusters = cluster(iai, args.num_parts,
                               [0, institution_offset, paper_offset, paper_offset + num_nodes_dict["paper"]],
                               adj, 1)
        apa_clusters = cluster(apa, args.num_parts,
                               [0, field_of_study_offset, paper_offset + num_nodes_dict["paper"]],
                               adj, 0)
        fpf_clusters = cluster(fpf, args.num_parts,
                               [0, field_of_study_offset, institution_offset, paper_offset + num_nodes_dict["paper"]],
                               adj, 1)
        print("num of nodes in each cluster:{}".format(len(pap_clusters[0])))
        p2p_influential_nodes = global_clusters(adj, p2p_clusters, limit, graph.in_degrees())
        pap_influential_nodes = global_clusters(adj, pap_clusters, limit, graph.in_degrees())
        ppp_influential_nodes = global_clusters(adj, ppp_clusters, limit, graph.in_degrees())
        iai_influential_nodes = global_clusters(adj, iai_clusters, limit, graph.in_degrees())
        apa_influential_nodes = global_clusters(adj, apa_clusters, limit, graph.in_degrees())
        fpf_influential_nodes = global_clusters(adj, fpf_clusters, limit, graph.in_degrees())
        
        print("num of nodes in each influential nodes:{}".format(len(p2p_influential_nodes)))
        print("allocate time:{:.4f}s".format(time.time() - start))
        with open(args.save_path + f"/mag-{args.model}-partition{args.num_parts}.pkl",
                  mode='wb') as f:
            pickle.dump((p2p_clusters, pap_clusters, ppp_clusters, iai_clusters, apa_clusters, fpf_clusters,
                         p2p_influential_nodes, pap_influential_nodes, ppp_influential_nodes, iai_influential_nodes,
                         apa_influential_nodes, fpf_influential_nodes), f)
    else:
        with open(args.save_path + f"/mag-{args.model}-partition{args.num_parts}.pkl",
                  mode='rb') as f:
            p2p_clusters, pap_clusters, ppp_clusters, iai_clusters, apa_clusters, fpf_clusters, \
            p2p_influential_nodes, pap_influential_nodes, ppp_influential_nodes, iai_influential_nodes, \
            apa_influential_nodes, fpf_influential_nodes = pickle.load(f)
    
    label_emb = torch.zeros((graph.num_nodes(), num_classes))
    mask = torch.tensor([False for i in range(graph.num_nodes())])
    wo_mask_train_idx = torch.tensor([True for i in range(train_idx.shape[0])])
    wo_mask_train_idx[:int(train_idx.shape[0] * args.mask)] = False
    wo_mask_train_idx = wo_mask_train_idx.tolist()
    random.shuffle(wo_mask_train_idx)
    mask[train_idx[wo_mask_train_idx] + paper_offset] = True
    one_hot = F.one_hot(labels[paper_offset:][train_idx[wo_mask_train_idx]], num_classes=num_classes)
    label_emb[mask] = one_hot.float()
    # graph.ndata["label_emb"] = label_emb

    metapaths = ["ppp", "pap"]
    clusters = {"pp": p2p_clusters, "pap": pap_clusters, "ppp": ppp_clusters, "iai": iai_clusters, "apa": apa_clusters, "fpf": fpf_clusters}
    influential_nodes = {"pp": p2p_influential_nodes, "pap": pap_influential_nodes, "ppp": ppp_influential_nodes,
        "iai": iai_influential_nodes, "apa": apa_influential_nodes, "fpf": fpf_influential_nodes}
    subgraphs = []

    cluster_data = [ClusterDataset(clusters[metapaths[0]], influential_nodes[metapaths[0]], graph),
                    ClusterDataset(clusters[metapaths[1]], influential_nodes[metapaths[1]], graph)]
    for i in range(args.num_parts):
        subgraphs.append(cluster_data[0][i])
        subgraphs.append(cluster_data[1][i])
    print(subgraphs[0].ndata[dgl.NID])
    dataset = Mydataset(subgraphs)
    # train_sampler = torch.utils.data.distributed.DistributedSampler(dataset, shuffle=False)
    train_loader = dgl.dataloading.GraphDataLoader(dataset, batch_size=2, drop_last=False, num_workers=4, use_ddp=True, shuffle=False)
    subgraphs = []
    for i in range(len(cluster_data)):
        for j in range(args.num_parts):
            subgraphs.append(cluster_data[i][j])
    test_dataset = Mydataset(subgraphs)
    # test_sampler = torch.utils.data.distributed.DistributedSampler(test_dataset, shuffle=False)
    test_loader = dgl.dataloading.GraphDataLoader(test_dataset, batch_size=8, drop_last=False, num_workers=4, use_ddp=True, shuffle=False)
    print("training")
    training(train_loader, test_loader, torch.max(graph.edata["_TYPE"]).item() + 1,
            feat, args, num_classes, labels, num_nodes_dict, paper_offset, train_idx, val_idx, test_idx, label_emb)
    print("all time:{:.4f}s".format(time.time() - start))
    