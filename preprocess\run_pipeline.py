import os
import argparse
import subprocess
import time

def run_command(command, description):
    """
    运行命令并打印相关信息
    
    参数:
    command: 要运行的命令
    description: 命令的描述
    """
    print(f"\n{'='*80}")
    print(f"Running: {description}")
    print(f"Command: {command}")
    print(f"{'='*80}\n")
    
    start_time = time.time()
    process = subprocess.Popen(command, shell=True)
    process.wait()
    end_time = time.time()
    
    if process.returncode != 0:
        print(f"\n{'='*80}")
        print(f"Error running command: {command}")
        print(f"Return code: {process.returncode}")
        print(f"{'='*80}\n")
        return False
    
    print(f"\n{'='*80}")
    print(f"Command completed successfully in {end_time - start_time:.2f} seconds")
    print(f"{'='*80}\n")
    return True

def main():
    parser = argparse.ArgumentParser(description='Run the complete data processing pipeline')
    parser.add_argument('--data_dir', type=str, default='../dataset', help='Directory containing dataset files')
    parser.add_argument('--embeddings_dir', type=str, default='../embeddings', help='Directory to save generated embeddings')
    parser.add_argument('--output_dir', type=str, default='../dataset_with_embeddings', help='Directory to save integrated datasets')
    parser.add_argument('--datasets', type=str, nargs='+', default=None, 
                        help='Specific datasets to process (default: all)')
    parser.add_argument('--dim', type=int, default=200, help='Embedding dimension')
    parser.add_argument('--epochs', type=int, default=100, help='Number of training epochs')
    parser.add_argument('--gpu', type=int, default=0, help='GPU device ID, -1 for CPU')
    args = parser.parse_args()
    
    # 创建必要的目录
    os.makedirs(args.data_dir, exist_ok=True)
    os.makedirs(args.embeddings_dir, exist_ok=True)
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 构建数据集参数字符串
    datasets_str = ""
    if args.datasets:
        datasets_str = " ".join(args.datasets)
    
    # 步骤1: 生成ComplEx嵌入
    cmd = f"python process_datasets.py --data_dir {args.data_dir} --output_dir {args.embeddings_dir} --dim {args.dim} --epochs {args.epochs} --gpu {args.gpu}"
    if args.datasets:
        cmd += f" --datasets {datasets_str}"
    
    if not run_command(cmd, "Generating ComplEx embeddings"):
        print("Error generating ComplEx embeddings. Pipeline stopped.")
        return
    
    # 步骤2: 将嵌入集成到数据集中
    cmd = f"python integrate_embeddings.py --data_dir {args.data_dir} --embeddings_dir {args.embeddings_dir} --output_dir {args.output_dir}"
    if args.datasets:
        cmd += f" --datasets {datasets_str}"
    
    if not run_command(cmd, "Integrating embeddings into datasets"):
        print("Error integrating embeddings. Pipeline stopped.")
        return
    
    # 步骤3: 运行Data_preprocess.py
    cmd = f"python Data_preprocess.py"
    if not run_command(cmd, "Running Data_preprocess.py"):
        print("Error running Data_preprocess.py. Pipeline stopped.")
        return
    
    print("\n" + "="*80)
    print("Pipeline completed successfully!")
    print("="*80 + "\n")
    
    print("Generated embeddings are saved in:", args.embeddings_dir)
    print("Integrated datasets are saved in:", args.output_dir)
    print("Final processed data is ready for model training.")

if __name__ == "__main__":
    main() 