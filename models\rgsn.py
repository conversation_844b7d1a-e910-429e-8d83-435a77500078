import torch  # 导入PyTorch库
from torch import nn  # 导入PyTorch神经网络模块
import torch.nn.functional as F  # 导入PyTorch函数式接口
import dgl  # 导入深度图学习库
import dgl.function as fn  # 导入DGL函数模块
from .common import FeedForwardNet, FeatAttention  # 从common模块导入前馈网络和特征注意力


class RGSN_layer(nn.Module):  # 定义关系图神经网络层
    def __init__(self,
                 out_feats,  # 输出特征维度
                 num_heads,  # 多头注意力的头数
                 num_etypes,  # 边类型数量
                 attn_drop=0.2):  # 注意力dropout率
        super(RGSN_layer, self).__init__()  # 调用父类初始化方法
        self._out_feats = out_feats  # 设置输出特征维度
        self._num_etypes = num_etypes  # 设置边类型数量
        self._num_heads = num_heads  # 设置头数
        self.attn_dropout = nn.Dropout(attn_drop)  # 创建注意力dropout层
        self.leaky_relu = nn.LeakyReLU(0.2)  # 创建LeakyReLU激活函数
        self.attn_layer = FeatAttention(out_feats, num_heads, attn_drop, negative_slope=0.2)  # 创建特征注意力层
        self.intra_attn_l = nn.Parameter(torch.Tensor(num_etypes, 1, num_heads, out_feats), requires_grad=True)  # 创建左侧内部注意力参数
        self.intra_attn_r = nn.Parameter(torch.Tensor(num_etypes, 1, num_heads, out_feats), requires_grad=True)  # 创建右侧内部注意力参数
        self.reset_parameters()  # 重置参数

    def reset_parameters(self):  # 参数重置方法
        gain = nn.init.calculate_gain('relu')  # 计算ReLU激活函数的增益值
        nn.init.xavier_normal_(self.intra_attn_l, gain=gain)  # 使用Xavier正态分布初始化左侧注意力参数
        nn.init.xavier_normal_(self.intra_attn_r, gain=gain)  # 使用Xavier正态分布初始化右侧注意力参数

    def forward(self, graph, feat):  # 前向传播方法
        with graph.local_scope():  # 使用局部作用域
            feat = F.normalize(feat)  # 对特征进行归一化
            feat_skip = [feat.view(-1, self._num_heads, self._out_feats)]  # 初始化特征跳跃连接列表并重塑特征维度
            graph.ndata["feat"] = feat.view(-1, self._num_heads, self._out_feats)  # 设置图节点特征并重塑维度
            for i in range(self._num_etypes):  # 遍历每种边类型
                mask = graph.edata["_TYPE"] == i  # 创建当前边类型的掩码
                subgraph = graph.edge_subgraph(mask, preserve_nodes=True)  # 根据掩码创建子图
                graph = dgl.add_self_loop(graph)  # 添加自环
                subgraph.update_all(fn.copy_u("feat", "msg"),  # 消息传递：复制源节点特征作为消息
                                    fn.mean("msg", "feat_neighbor"))  # 消息聚合：计算邻居消息的平均值
                graph = dgl.remove_self_loop(graph)  # 移除自环
                # subgraph.ndata["feat_neighbor"] += graph.ndata["feat"] = feat  # 注释掉的代码行
                # mean_feat = F.normalize(subgraph.ndata.pop("feat_neighbor"))  # 注释掉的代码行
                # mean_feat = subgraph.ndata.pop("feat_neighbor")  # 注释掉的代码行

                subgraph.ndata["left"] = (subgraph.ndata["feat"] * self.intra_attn_l[i]).sum(-1).unsqueeze(-1)  # 计算左侧注意力分数
                subgraph.ndata["right"] = (subgraph.ndata["feat_neighbor"] * self.intra_attn_r[i]).sum(-1).unsqueeze(-1)  # 计算右侧注意力分数
                # subgraph.apply_edges(lambda edges: {'a' : (edges.src["feat"] * self.intra_attn_l[i]).sum(-1) + (mean_feat[edges.src["_ID"]] * self.intra_attn_r[i]).sum(-1)})  # 注释掉的代码行
                subgraph.apply_edges(fn.u_add_v("left", "right", "a"))  # 计算边上的注意力分数（左+右）
                subgraph.edata["a"] = self.leaky_relu(subgraph.edata["a"].unsqueeze(-1))  # 应用LeakyReLU激活函数
                subgraph.edata["a"] = dgl.nn.functional.edge_softmax(subgraph, subgraph.edata["a"], norm_by='dst')  # 对边注意力分数应用softmax
                subgraph.edata["a"] = self.attn_dropout(subgraph.edata["a"]).view(-1, self._num_heads, 1)  # 应用注意力dropout并重塑维度
                subgraph.update_all(fn.u_mul_e("feat", "a", "msg"),  # 消息传递：源节点特征与注意力权重相乘
                             fn.sum("msg", "feat_neighbor"))  # 消息聚合：对消息求和
                feat_skip.append(subgraph.ndata.pop("feat_neighbor"))  # 添加聚合后的邻居特征到跳跃连接列表
            feat = self.attn_layer(feat_skip).view(-1, self._num_heads * self._out_feats)  # 应用特征注意力层并重塑维度
            feat = F.relu(feat)  # 应用ReLU激活函数
            return feat  # 返回处理后的特征


class RGSN(nn.Module):  # 定义关系图神经网络模型
    def __init__(self, in_feats, hidden, out_feats, dropout, input_dropout, num_layers, num_etypes, num_heads):  # 初始化方法
        super(RGSN, self).__init__()  # 调用父类初始化方法
        self.num_layers = num_layers  # 设置层数
        self.out_feats = out_feats  # 设置输出特征维度
        self.hidden = hidden  # 设置隐藏层维度
        self.input_dropout = nn.Dropout(input_dropout)  # 创建输入dropout层
        self.dropout = nn.Dropout(dropout)  # 创建常规dropout层
        self.rgat = nn.ModuleList()  # 创建RGSN层列表
        for i in range(num_layers):  # 创建所有RGSN层
            self.rgat.append(RGSN_layer(hidden // num_heads, num_heads, num_etypes))  # 添加RGSN层

        self.mlp = nn.Sequential(  # 创建多层感知机
            nn.Linear(hidden, hidden),  # 第一个线性层
            nn.ReLU(),  # ReLU激活函数
            nn.Dropout(dropout),  # Dropout层
            nn.Linear(hidden, hidden),  # 第二个线性层
            nn.ReLU(),  # ReLU激活函数
            nn.Dropout(dropout),  # Dropout层
            nn.Linear(hidden, out_feats)  # 输出线性层
        )
        self.linear = nn.Linear(in_feats, hidden)  # 输入特征转换线性层
        self.label_linear = nn.Linear(out_feats, hidden)  # 标签特征转换线性层
        self.norms = nn.ModuleList([nn.BatchNorm1d(hidden) for i in range(num_layers + 1)])  # 创建批归一化层列表
        self.inception_ffs = nn.ModuleList()  # 创建Inception风格前馈网络列表
        for hop in range(num_layers + 1):  # 为每一跳创建前馈网络
            self.inception_ffs.append(
                FeedForwardNet(hidden, hidden, hidden, 1, dropout))  # 添加前馈网络

    def forward(self, graph, feat, label_feat):  # 前向传播方法
        feat = self.linear(self.input_dropout(feat))  # 应用输入dropout并转换输入特征
        label_feat = self.label_linear(self.input_dropout(label_feat))  # 应用输入dropout并转换标签特征
        feat += label_feat  # 将特征和标签特征相加
        feat_hop = [feat]  # 初始化特征跳跃连接列表
        for i in range(self.num_layers):  # 遍历每一层
            feat = self.rgat[i](graph, feat)  # 通过RGSN层处理特征
            feat_hop.append(feat)  # 添加处理后的特征到跳跃连接列表
        feat_hop = [self.input_dropout(feat_h) for feat_h in feat_hop]  # 对每层的特征应用输入dropout

        norm_feats = []  # 初始化归一化特征列表
        for _feat, ff in zip(feat_hop, self.norms):  # 遍历特征和归一化层
            norm_feats.append(ff(_feat))  # 应用批归一化并添加到列表

        tmp = []  # 初始化临时特征列表
        for _feat, _ff in zip(norm_feats, self.inception_ffs):  # 遍历归一化特征和前馈网络
            tmp.append(_ff(_feat))  # 应用前馈网络
        feat = self.dropout((sum(tmp) / len(tmp)).view(-1, self.hidden))  # 计算平均值，重塑并应用dropout
        return self.mlp(feat)  # 通过MLP处理并返回最终特征