import argparse
import os
import pickle
import random
import warnings
import scipy.sparse as sp
import dgl
import dgl.function as fn
import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F
from tqdm import tqdm
import time
from sklearn.metrics import roc_auc_score, average_precision_score
from models import NARS, SeHGNN
from torch.utils.data import Dataset
import torch.distributed as dist
from utils import get_n_params, distributed_sum


warnings.filterwarnings("ignore")
parser = argparse.ArgumentParser(description='DBLP Link Prediction with NARS and SeHGNN')
parser.add_argument('--device', type=int, default=0)
parser.add_argument('--num_layer', type=int, default=3)
parser.add_argument('--hidden_channel', type=int, default=256)  # As specified: hidden layer size is 256
parser.add_argument('--dropout', type=float, default=0.1)       # As specified: sigma is 0.1 for DBLP
parser.add_argument('--lr', type=float, default=0.001)
parser.add_argument('--epochs', type=int, default=300)
parser.add_argument('--batch_size', type=int, default=1000)     # As specified: batch size is 1000
parser.add_argument('--runs', type=int, default=1)
parser.add_argument('--save_path', type=str, default="../partition", help="Partition save path")
parser.add_argument('--model', type=str, default="nars", help="Model selection: nars or sehgnn")
parser.add_argument('--local_rank', type=int, default=0, help="Local rank for distributed training")
args = parser.parse_args()
print(args)


def load_DBLP():
    """
    Load DBLP dataset and prepare it for link prediction
    """
    path = "../dataset/DBLP/"
    with open(path + "DBLP.pkl", mode="rb") as f:
        edge_index_dict, feature, train_idx, test_idx, labels, num_nodes_dict = pickle.load(f)

    num_phrase = 217557
    num_author = 1766361
    num_venue = 5076
    num_year = 83
    idx_author = num_phrase
    idx_venue = num_phrase + num_author
    idx_year = num_phrase + num_author + num_venue
    num_nodes = num_phrase + num_author + num_venue + num_year
    phrase_emb = feature["phrase"]
    author_emb = feature["author"]
    venue_emb = feature["venue"]
    year_emb = feature["year"]

    new_edge_index_dict = {}
    for key in edge_index_dict.keys():
        new_edge_index_dict[key] = (edge_index_dict[key][0], edge_index_dict[key][1])

    g = dgl.heterograph(new_edge_index_dict)
    g.nodes["author"].data["feat"] = author_emb
    g.nodes["phrase"].data["feat"] = phrase_emb
    g.nodes["venue"].data["feat"] = venue_emb
    g.nodes["year"].data["feat"] = year_emb

    # Convert to homogeneous graph for link prediction
    graph = dgl.to_homogeneous(g, ndata=["feat"])
    
    # Get node features
    feat = graph.ndata.pop("feat")
    
    print(f"# Nodes: {graph.number_of_nodes()}\n"
          f"# Edges: {graph.number_of_edges()}")

    return graph, feat, num_nodes_dict


def split_edges_for_link_prediction(graph, num_train_edges=250000, num_test_edges=750000, num_neg_train_samples=25000):
    """
    Split edges for link prediction task with specified numbers of edges
    """
    # Create a new graph with all nodes but no edges
    src, dst = graph.edges()
    edges = torch.stack([src, dst], dim=0)
    
    # Shuffle edges
    perm = torch.randperm(edges.size(1))
    edges = edges[:, perm]
    
    # Select train and test edges
    train_edges = edges[:, :num_train_edges]
    test_edges = edges[:, num_train_edges:num_train_edges+num_test_edges]
    
    # Create training graph
    train_graph = dgl.graph((train_edges[0], train_edges[1]), num_nodes=graph.number_of_nodes())
    
    # Generate negative samples for training
    neg_src = torch.randint(0, graph.number_of_nodes(), (num_neg_train_samples,))
    neg_dst = torch.randint(0, graph.number_of_nodes(), (num_neg_train_samples,))
    train_neg_edges = torch.stack([neg_src, neg_dst], dim=0)
    
    # Generate negative samples for testing
    neg_src = torch.randint(0, graph.number_of_nodes(), (num_test_edges,))
    neg_dst = torch.randint(0, graph.number_of_nodes(), (num_test_edges,))
    test_neg_edges = torch.stack([neg_src, neg_dst], dim=0)
    
    return train_graph, train_edges, train_neg_edges, test_edges, test_neg_edges


class LinkPredictor(nn.Module):
    def __init__(self, in_channels, hidden_channels):
        super(LinkPredictor, self).__init__()
        self.lin1 = nn.Linear(in_channels * 2, hidden_channels)
        self.lin2 = nn.Linear(hidden_channels, 1)
    
    def forward(self, x_i, x_j):
        x = torch.cat([x_i, x_j], dim=1)
        x = self.lin1(x)
        x = F.relu(x)
        x = F.dropout(x, p=0.1, training=self.training)
        x = self.lin2(x)
        return torch.sigmoid(x)


def get_edge_embeddings(embeddings, edges):
    """
    Get node embeddings for the given edges
    """
    return embeddings[edges[0]], embeddings[edges[1]]


def train(model, predictor, train_graph, train_pos_edges, train_neg_edges, optimizer, feat, batch_size=1000, device='cuda'):
    """
    Train the model on the given graph
    """
    model.train()
    predictor.train()
    
    # Prepare positive and negative edge data
    train_edges = torch.cat([train_pos_edges, train_neg_edges], dim=1)
    train_labels = torch.zeros(train_edges.shape[1])
    train_labels[:train_pos_edges.shape[1]] = 1.0
    
    # Process in batches
    total_loss = 0
    for start in range(0, train_edges.shape[1], batch_size):
        end = min(start + batch_size, train_edges.shape[1])
        batch_edges = train_edges[:, start:end]
        batch_labels = train_labels[start:end].to(device)
        
        optimizer.zero_grad()
        
        # Forward pass
        node_embeddings = model(train_graph, feat.to(device), torch.zeros_like(feat[:, :model.out_feats]).to(device))
        src_embeds, dst_embeds = get_edge_embeddings(node_embeddings, batch_edges.to(device))
        pred = predictor(src_embeds, dst_embeds).squeeze()
        
        # Calculate loss
        loss = F.binary_cross_entropy(pred, batch_labels)
        
        # Backward pass
        loss.backward()
        optimizer.step()
        
        total_loss += loss.item() * (end - start)
    
    return total_loss / train_edges.shape[1]


@torch.no_grad()
def test(model, predictor, train_graph, test_pos_edges, test_neg_edges, feat, batch_size=1000, device='cuda'):
    """
    Test the model on the test data
    """
    model.eval()
    predictor.eval()
    
    # Combine positive and negative edges for evaluation
    test_edges = torch.cat([test_pos_edges, test_neg_edges], dim=1)
    test_labels = torch.zeros(test_edges.shape[1])
    test_labels[:test_pos_edges.shape[1]] = 1.0
    
    # Get node embeddings
    node_embeddings = model(train_graph, feat.to(device), torch.zeros_like(feat[:, :model.out_feats]).to(device))
    
    # Process in batches
    y_pred = []
    for start in range(0, test_edges.shape[1], batch_size):
        end = min(start + batch_size, test_edges.shape[1])
        batch_edges = test_edges[:, start:end]
        
        # Get embeddings for edges
        src_embeds, dst_embeds = get_edge_embeddings(node_embeddings, batch_edges.to(device))
        
        # Predict
        pred = predictor(src_embeds, dst_embeds).squeeze().cpu()
        y_pred.append(pred)
    
    y_pred = torch.cat(y_pred, dim=0).numpy()
    y_true = test_labels.numpy()
    
    # Calculate evaluation metrics
    auc = roc_auc_score(y_true, y_pred)
    ap = average_precision_score(y_true, y_pred)
    
    return auc, ap


def main():
    # Set device
    device = f'cuda:{args.device}' if torch.cuda.is_available() else 'cpu'
    
    # Load DBLP dataset
    graph, feat, num_nodes_dict = load_DBLP()
    
    # Split edges for link prediction
    train_graph, train_pos_edges, train_neg_edges, test_pos_edges, test_neg_edges = split_edges_for_link_prediction(
        graph, 
        num_train_edges=250000,      # As specified: 250k edges for training
        num_test_edges=750000,       # As specified: 750k edges for testing
        num_neg_train_samples=25000  # As specified: 25k negative samples for training
    )
    
    # Get edge types
    num_etypes = train_graph.edata.get("_TYPE", torch.zeros(train_graph.number_of_edges(), dtype=torch.long)).max().item() + 1
    
    # Create model
    in_feats = feat.shape[1]
    num_classes = 1  # Binary classification for link prediction
    
    for r in range(args.runs):
        # Initialize model based on choice
        if args.model == "nars":
            model = NARS(in_feats, args.hidden_channel, args.hidden_channel, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        else:  # sehgnn
            model = SeHGNN(in_feats, args.hidden_channel, args.hidden_channel, args.dropout, 0.0, args.num_layer, num_etypes, 4)
        
        model = model.to(device)
        print(f"Model: {args.model}")
        print("# Params:", get_n_params(model))
        
        # Initialize link predictor
        predictor = LinkPredictor(args.hidden_channel, args.hidden_channel // 2).to(device)
        
        # Initialize optimizer
        optimizer = torch.optim.Adam(
            list(model.parameters()) + list(predictor.parameters()), 
            lr=args.lr
        )
        
        # Training
        best_auc = 0
        for epoch in range(1, args.epochs + 1):
            t1 = time.time()
            
            # Train
            loss = train(
                model, predictor, train_graph, train_pos_edges, train_neg_edges, 
                optimizer, feat, args.batch_size, device
            )
            
            t2 = time.time()
            
            # Test
            auc, ap = test(
                model, predictor, train_graph, test_pos_edges, test_neg_edges, 
                feat, args.batch_size, device
            )
            
            t3 = time.time()
            
            # Update best results
            if auc > best_auc:
                best_auc = auc
            
            # Print log
            log = "Epoch {}, Loss: {:.4f}, AUC: {:.4f}, AP: {:.4f}".format(epoch, loss, auc, ap)
            log += ", Training Time(s): {:.4f}, Inference Time(s): {:.4f}".format(t2 - t1, t3 - t2)
            print(log)
        
        print(f"Best AUC: {best_auc:.4f}")


if __name__ == "__main__":
    main()
