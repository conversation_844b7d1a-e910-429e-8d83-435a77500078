import os
import torch
import numpy as np
import pickle
import argparse
from tqdm import tqdm

def integrate_embeddings_dblp(data_path, embeddings_dir, output_path):
    """
    将ComplEx嵌入集成到DBLP数据集中
    
    参数:
    data_path: DBLP数据集的路径
    embeddings_dir: 嵌入目录的路径
    output_path: 输出文件的路径
    """
    print(f"Integrating embeddings for DBLP dataset...")
    
    # 加载原始数据
    with open(data_path, mode="rb") as f:
        edge_index_dict, feature_dict, train_idx, test_idx, labels, num_nodes_dict = pickle.load(f)
    
    # 检查哪些节点类型缺少特征
    for ntype in num_nodes_dict.keys():
        if ntype not in feature_dict:
            print(f"Node type {ntype} lacks features, loading ComplEx embeddings...")
            
            # 加载ComplEx嵌入
            emb_path = os.path.join(embeddings_dir, f"DBLP_{ntype}_complex_emb.npy")
            if not os.path.exists(emb_path):
                print(f"Warning: Embedding file {emb_path} not found!")
                continue
            
            embeddings = np.load(emb_path)
            feature_dict[ntype] = torch.FloatTensor(embeddings)
            print(f"Loaded embeddings for {ntype} with shape {feature_dict[ntype].shape}")
    
    # 保存集成后的数据
    with open(output_path, mode="wb") as f:
        pickle.dump((edge_index_dict, feature_dict, train_idx, test_idx, labels, num_nodes_dict), f)
    
    print(f"Integrated data saved to {output_path}")

def integrate_embeddings_yelp(data_path, embeddings_dir, output_path):
    """
    将ComplEx嵌入集成到Yelp数据集中
    
    参数:
    data_path: Yelp数据集的路径
    embeddings_dir: 嵌入目录的路径
    output_path: 输出文件的路径
    """
    print(f"Integrating embeddings for Yelp dataset...")
    
    # 加载原始数据
    with open(data_path, mode="rb") as f:
        edge_index_dict, features, pos_link_s, pos_link_t, test_link_s, test_link_t, flags, num_nodes_dict = pickle.load(f)
    
    # 检查哪些节点类型缺少特征
    for ntype in num_nodes_dict.keys():
        if ntype not in features:
            print(f"Node type {ntype} lacks features, loading ComplEx embeddings...")
            
            # 加载ComplEx嵌入
            emb_path = os.path.join(embeddings_dir, f"Yelp_{ntype}_complex_emb.npy")
            if not os.path.exists(emb_path):
                print(f"Warning: Embedding file {emb_path} not found!")
                continue
            
            embeddings = np.load(emb_path)
            features[ntype] = torch.FloatTensor(embeddings)
            print(f"Loaded embeddings for {ntype} with shape {features[ntype].shape}")
    
    # 保存集成后的数据
    with open(output_path, mode="wb") as f:
        pickle.dump((edge_index_dict, features, pos_link_s, pos_link_t, test_link_s, test_link_t, flags, num_nodes_dict), f)
    
    print(f"Integrated data saved to {output_path}")

def integrate_embeddings_pubmed(data_path, embeddings_dir, output_path):
    """
    将ComplEx嵌入集成到PubMed数据集中
    
    参数:
    data_path: PubMed数据集的路径
    embeddings_dir: 嵌入目录的路径
    output_path: 输出文件的路径
    """
    print(f"Integrating embeddings for PubMed dataset...")
    
    # 加载原始数据
    with open(data_path, mode="rb") as f:
        edge_index_dict, feature_dict, train_idx, test_idx, labels, num_nodes_dict = pickle.load(f)
    
    # 检查哪些节点类型缺少特征
    for ntype in num_nodes_dict.keys():
        if ntype not in feature_dict:
            print(f"Node type {ntype} lacks features, loading ComplEx embeddings...")
            
            # 加载ComplEx嵌入
            emb_path = os.path.join(embeddings_dir, f"Pubmed_{ntype}_complex_emb.npy")
            if not os.path.exists(emb_path):
                print(f"Warning: Embedding file {emb_path} not found!")
                continue
            
            embeddings = np.load(emb_path)
            feature_dict[ntype] = torch.FloatTensor(embeddings)
            print(f"Loaded embeddings for {ntype} with shape {feature_dict[ntype].shape}")
    
    # 保存集成后的数据
    with open(output_path, mode="wb") as f:
        pickle.dump((edge_index_dict, feature_dict, train_idx, test_idx, labels, num_nodes_dict), f)
    
    print(f"Integrated data saved to {output_path}")

def integrate_embeddings_freebase(data_path, embeddings_dir, output_path):
    """
    将ComplEx嵌入集成到Freebase数据集中
    
    参数:
    data_path: Freebase数据集的路径
    embeddings_dir: 嵌入目录的路径
    output_path: 输出文件的路径
    """
    print(f"Integrating embeddings for Freebase dataset...")
    
    # 加载原始数据
    with open(data_path, mode="rb") as f:
        edge_index_dict, train_idx, test_idx, labels, num_nodes_dict = pickle.load(f)
    
    # 创建特征字典
    feature_dict = {}
    
    # 为所有节点类型加载ComplEx嵌入
    for ntype in num_nodes_dict.keys():
        print(f"Loading ComplEx embeddings for node type {ntype}...")
        
        # 加载ComplEx嵌入
        emb_path = os.path.join(embeddings_dir, f"Freebase_{ntype}_complex_emb.npy")
        if not os.path.exists(emb_path):
            print(f"Warning: Embedding file {emb_path} not found!")
            continue
        
        embeddings = np.load(emb_path)
        feature_dict[ntype] = torch.FloatTensor(embeddings)
        print(f"Loaded embeddings for {ntype} with shape {feature_dict[ntype].shape}")
    
    # 保存集成后的数据
    with open(output_path, mode="wb") as f:
        pickle.dump((edge_index_dict, feature_dict, train_idx, test_idx, labels, num_nodes_dict), f)
    
    print(f"Integrated data saved to {output_path}")

def integrate_embeddings_ogbn_mag(data_path, embeddings_dir, output_path):
    """
    将ComplEx嵌入集成到ogbn-mag数据集中
    
    参数:
    data_path: ogbn-mag数据集的路径
    embeddings_dir: 嵌入目录的路径
    output_path: 输出文件的路径
    """
    print(f"Integrating embeddings for ogbn-mag dataset...")
    
    # 加载原始数据
    from ogb.nodeproppred import DglNodePropPredDataset
    dataset = DglNodePropPredDataset(name='ogbn-mag', root=data_path)
    graph, labels = dataset[0]
    
    # 检查哪些节点类型缺少特征
    for ntype in graph.ntypes:
        if 'feat' not in graph.nodes[ntype].data:
            print(f"Node type {ntype} lacks features, loading ComplEx embeddings...")
            
            # 加载ComplEx嵌入
            emb_path = os.path.join(embeddings_dir, f"ogbn-mag_{ntype}_complex_emb.npy")
            if not os.path.exists(emb_path):
                print(f"Warning: Embedding file {emb_path} not found!")
                continue
            
            embeddings = np.load(emb_path)
            graph.nodes[ntype].data['feat'] = torch.FloatTensor(embeddings)
            print(f"Loaded embeddings for {ntype} with shape {graph.nodes[ntype].data['feat'].shape}")
    
    # 保存集成后的数据
    torch.save((graph, labels), output_path)
    
    print(f"Integrated data saved to {output_path}")

def main():
    parser = argparse.ArgumentParser(description='Integrate ComplEx embeddings into datasets')
    parser.add_argument('--data_dir', type=str, default='../dataset', help='Directory containing dataset files')
    parser.add_argument('--embeddings_dir', type=str, default='../embeddings', help='Directory containing generated embeddings')
    parser.add_argument('--output_dir', type=str, default='../dataset_with_embeddings', help='Directory to save integrated datasets')
    parser.add_argument('--datasets', type=str, nargs='+', default=None, 
                        help='Specific datasets to process (default: all)')
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 默认处理所有5个数据集
    all_datasets = ['DBLP', 'Yelp', 'Pubmed', 'Freebase', 'ogbn-mag']
    datasets_to_process = args.datasets if args.datasets else all_datasets
    
    print(f"Will integrate embeddings for the following datasets: {datasets_to_process}")
    
    # 处理每个数据集
    for dataset_name in datasets_to_process:
        if dataset_name not in all_datasets:
            print(f"Warning: Unknown dataset {dataset_name}, skipping...")
            continue
        
        print(f"\n{'='*50}")
        print(f"Integrating embeddings for dataset: {dataset_name}")
        print(f"{'='*50}")
        
        if dataset_name == 'DBLP':
            data_path = os.path.join(args.data_dir, "DBLP.pkl")
            output_path = os.path.join(args.output_dir, "DBLP_with_embeddings.pkl")
            integrate_embeddings_dblp(data_path, args.embeddings_dir, output_path)
        
        elif dataset_name == 'Yelp':
            data_path = os.path.join(args.data_dir, "Yelp.pkl")
            output_path = os.path.join(args.output_dir, "Yelp_with_embeddings.pkl")
            integrate_embeddings_yelp(data_path, args.embeddings_dir, output_path)
        
        elif dataset_name == 'Pubmed':
            data_path = os.path.join(args.data_dir, "Pubmed.pkl")
            output_path = os.path.join(args.output_dir, "Pubmed_with_embeddings.pkl")
            integrate_embeddings_pubmed(data_path, args.embeddings_dir, output_path)
        
        elif dataset_name == 'Freebase':
            data_path = os.path.join(args.data_dir, "Freebase.pkl")
            output_path = os.path.join(args.output_dir, "Freebase_with_embeddings.pkl")
            integrate_embeddings_freebase(data_path, args.embeddings_dir, output_path)
        
        elif dataset_name == 'ogbn-mag':
            output_path = os.path.join(args.output_dir, "ogbn_mag_with_embeddings.pt")
            integrate_embeddings_ogbn_mag(args.data_dir, args.embeddings_dir, output_path)
        
        print(f"{'='*50}")
        print(f"Finished integrating embeddings for dataset: {dataset_name}")
        print(f"{'='*50}\n")
    
    print("All embeddings integrated successfully!")

if __name__ == "__main__":
    main() 